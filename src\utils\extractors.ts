import * as pdfjs from 'pdfjs-dist';
import { Transaction } from '../types';

// Set up the worker for PDF.js
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.mjs',
  import.meta.url
).toString();

/**
 * Extract text content from a PDF file
 */
export const extractTextFromPDF = async (file: File): Promise<string[]> => {
  const fileArrayBuffer = await file.arrayBuffer();
  const pdf = await pdfjs.getDocument(new Uint8Array(fileArrayBuffer)).promise;
  const textContent: string[] = [];

  for (let i = 1; i <= pdf.numPages; i++) {
    const page = await pdf.getPage(i);
    const content = await page.getTextContent();
    const text = content.items.map(item => 'str' in item ? item.str : '').join(' ');
    textContent.push(text);
  }

  return textContent;
};

/**
 * Extract transactions from SBI bank statement format
 */
export const extractSBITransactions = (textContent: string[]): Transaction[] => {
  const transactions: Transaction[] = [];
  
  // Enhanced regex patterns to match more SBI statement formats
  const patterns = [
    // Format 1: Standard format with date, description, debit, credit, balance
    /(\d{2}\/\d{2}\/\d{4})\s+([^0-9]+?)(?:\s+(?:([\d,]+\.\d{2})\s+)|(?:\s+))(?:([\d,]+\.\d{2})\s+)?([\d,]+\.\d{2})/g,
    
    // Format 2: Alternative format with different spacing and optional transaction codes
    /(\d{2}\/\d{2}\/\d{4})\s+(?:BY|TO)?\s*([^0-9]+?)\s+(?:([\d,]+\.\d{2})|)\s*(?:([\d,]+\.\d{2})|)\s+([\d,]+\.\d{2})/g,
    
    // Format 3: Format with transaction codes and reference numbers
    /(\d{2}\/\d{2}\/\d{4})\s+(?:\w+\s+)?([^0-9]+?)(?:REF\s*NO\s*:\s*\d+)?\s*(?:([\d,]+\.\d{2}))?\s*(?:([\d,]+\.\d{2}))?\s+([\d,]+\.\d{2})/g,
    
    // Format 4: Format with INR currency symbol
    /(\d{2}\/\d{2}\/\d{4})\s+([^0-9]+?)(?:INR\s+)?([\d,]+\.\d{2})?\s*([\d,]+\.\d{2})?\s*([\d,]+\.\d{2})/g
  ];
  
  let matchFound = false;
  
  textContent.forEach((pageText, pageIndex) => {
    // Enhanced text cleaning
    const cleanText = pageText
      .replace(/\s+/g, ' ')  // Normalize whitespace
      .replace(/[^\x20-\x7E]/g, ' ') // Remove non-printable characters
      .replace(/(?:TRANSACTION DETAILS|DATE|DESCRIPTION|DEBIT|CREDIT|BALANCE|CHQ\/REF NO\.|VALUE DATE)/gi, '') // Remove headers
      .trim();
    
    patterns.forEach((pattern, patternIndex) => {
      let match;
      while ((match = pattern.exec(cleanText)) !== null) {
        matchFound = true;
        const [, date, description, debitStr, creditStr, balanceStr] = match;
        
        try {
          // Convert strings to numbers, removing commas and handling INR prefix
          const debit = debitStr ? parseFloat(debitStr.replace(/INR|\s|,/g, '')) : null;
          const credit = creditStr ? parseFloat(creditStr.replace(/INR|\s|,/g, '')) : null;
          const balance = parseFloat(balanceStr.replace(/INR|\s|,/g, ''));
          
          // Enhanced validation
          if (isNaN(balance)) {
            console.warn(`Invalid balance value on page ${pageIndex + 1}, pattern ${patternIndex}:`, balanceStr);
            continue;
          }
          
          if (debitStr && isNaN(debit)) {
            console.warn(`Invalid debit value on page ${pageIndex + 1}, pattern ${patternIndex}:`, debitStr);
            continue;
          }
          
          if (creditStr && isNaN(credit)) {
            console.warn(`Invalid credit value on page ${pageIndex + 1}, pattern ${patternIndex}:`, creditStr);
            continue;
          }
          
          transactions.push({
            id: `${date}-${Math.random().toString(36).substring(2, 9)}`,
            date,
            description: description.trim(),
            debit,
            credit,
            balance
          });
        } catch (error) {
          console.error(`Error processing SBI transaction on page ${pageIndex + 1}, pattern ${patternIndex}:`, error);
          continue;
        }
      }
    });
  });
  
  if (!matchFound) {
    throw new Error(
      'Unable to extract transactions from the SBI bank statement. ' +
      'This might be due to a different statement format or PDF structure. ' +
      'Please try manually selecting your bank format or contact support if the issue persists.'
    );
  }
  
  return transactions.sort((a, b) => {
    const dateA = new Date(a.date.split('/').reverse().join('-'));
    const dateB = new Date(b.date.split('/').reverse().join('-'));
    return dateA.getTime() - dateB.getTime();
  });
};

/**
 * Extract transactions from HDFC bank statement format
 */
export const extractHDFCTransactions = (textContent: string[]): Transaction[] => {
  const transactions: Transaction[] = [];
  const seenTransactions = new Set<string>(); // For deduplication
  
  // Validate date format (DD/MM/YY or DD/MM/YYYY)
  const isValidDate = (dateStr: string): boolean => {
    const dateRegex = /^(\d{2})\/(\d{2})\/(\d{2}|\d{4})$/;
    const match = dateStr.match(dateRegex);
    if (!match) return false;
    
    const [, day, month, year] = match;
    const dayNum = parseInt(day, 10);
    const monthNum = parseInt(month, 10);
    
    return dayNum >= 1 && dayNum <= 31 && monthNum >= 1 && monthNum <= 12;
  };
  
  // Validate amount format (X,XXX.XX)
  const isValidAmount = (amountStr: string): boolean => {
    return /^[\d,]+\.\d{2}$/.test(amountStr);
  };
  
  // Format amount to X,XXX.XX or 0.00
  const formatAmount = (amount: number | null): string => {
    if (amount === null || amount === 0) return '0.00';
    return amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  };
  
  // Convert 2-digit year to 4-digit year
  const normalizeDate = (dateStr: string): string => {
    const parts = dateStr.split('/');
    if (parts[2].length === 2) {
      const year = parseInt(parts[2], 10);
      // Assume years 00-30 are 2000-2030, 31-99 are 1931-1999
      const fullYear = year <= 30 ? 2000 + year : 1900 + year;
      return `${parts[0]}/${parts[1]}/${fullYear.toString().slice(-2)}`;
    }
    return dateStr;
  };
  
  // Create unique key for deduplication
  const createTransactionKey = (date: string, description: string, refNo: string): string => {
    const cleanDesc = description.replace(/\s+/g, ' ').trim().toLowerCase();
    return `${date}|${cleanDesc}|${refNo}`;
  };
  
  textContent.forEach((pageText, pageIndex) => {
    console.debug(`Processing HDFC statement page ${pageIndex + 1}`);
    
    // Skip pages that don't contain transaction data
    if (!pageText.includes('Date') || !pageText.includes('Narration') || pageText.trim().length < 100) {
      console.debug(`Skipping page ${pageIndex + 1} - insufficient transaction data`);
      return;
    }
    
    // Use regex to find transaction patterns in HDFC format
    // HDFC format: Date Narration Chq/Ref.No. Value Date Withdrawal Amt. Deposit Amt. Closing Balance
    const transactionPattern = /(\d{2}\/\d{2}\/\d{2,4})\s+([\s\S]*?)(?=\d{2}\/\d{2}\/\d{2,4}|$)/g;
    
    let match;
    while ((match = transactionPattern.exec(pageText)) !== null) {
      const [fullMatch, date, transactionData] = match;
      
      if (!isValidDate(date)) continue;
      
      // Clean and split the transaction data
      const cleanData = transactionData.replace(/\s+/g, ' ').trim();
      
      // Find all amounts in the transaction data
      const amountMatches = cleanData.match(/([\d,]+\.\d{2})/g) || [];
      const amounts = amountMatches.map(amt => parseFloat(amt.replace(/,/g, ''))).filter(amt => !isNaN(amt));
      
      if (amounts.length === 0) continue;
      
      // Extract description by removing amounts and dates from the data
      let description = cleanData;
      amountMatches.forEach(amt => {
        description = description.replace(amt, '');
      });
      
      // Remove additional date patterns (Value Date)
      description = description.replace(/\d{2}\/\d{2}\/\d{2,4}/g, '');
      
      // Clean up description
      description = description
        .replace(/\s+/g, ' ')
        .replace(/^(BY|TO|UPI|NEFT|IMPS|SETTLEMENT|Value Date)\s*/i, '')
        .replace(/\s*(Ref|ID|No)\s*:?\s*\d*\s*/gi, '')
        .trim();
      
      if (!description || description.length < 3) {
        description = 'Unknown Transaction';
      }
      
      // Extract reference number from original data
      let refNo = '';
      const refMatch = cleanData.match(/(?:Ref|ID|No)\s*:?\s*(\d+)/i);
      if (refMatch) {
        refNo = refMatch[1];
      }
      
      // Determine debit, credit, and balance based on HDFC statement structure
      let debit: number | null = null;
      let credit: number | null = null;
      let balance: number;
      
      // In HDFC statements, the last amount is always the closing balance
      balance = amounts[amounts.length - 1];
      
      // If we have more than one amount, determine debit/credit
      if (amounts.length >= 2) {
        // Check if this is a debit or credit transaction
        const descLower = description.toLowerCase();
        const isDebitTransaction = descLower.includes('withdrawal') || 
                                 descLower.includes('debit') || 
                                 descLower.includes('payment') || 
                                 descLower.includes('transfer') ||
                                 descLower.includes('upi') ||
                                 descLower.includes('neft') ||
                                 descLower.includes('imps');
        
        if (amounts.length === 2) {
          // Two amounts: transaction amount + balance
          if (isDebitTransaction) {
            debit = amounts[0];
          } else {
            credit = amounts[0];
          }
        } else if (amounts.length >= 3) {
          // Three or more amounts: might have both debit and credit, or multiple amounts
          // Take the second-to-last as the transaction amount
          const transactionAmount = amounts[amounts.length - 2];
          if (isDebitTransaction) {
            debit = transactionAmount;
          } else {
            credit = transactionAmount;
          }
        }
      }
      
      // Validate balance
      if (isNaN(balance) || balance < 0) continue;
      
      // Normalize date format
      const normalizedDate = normalizeDate(date);
      
      // Create unique key for deduplication
      const transactionKey = createTransactionKey(normalizedDate, description, refNo);
      
      // Skip if we've already seen this transaction
      if (seenTransactions.has(transactionKey)) {
        console.debug(`Skipping duplicate transaction: ${transactionKey}`);
        continue;
      }
      
      seenTransactions.add(transactionKey);
      
      // Add transaction
      transactions.push({
        id: `${normalizedDate}-${refNo || Math.random().toString(36).substring(2, 9)}`,
        date: normalizedDate,
        description,
        debit,
        credit,
        balance
      });
    }
  });
  
  if (transactions.length === 0) {
    console.error('HDFC Statement Content Sample:', textContent.slice(0, 2));
    throw new Error(
      'Unable to extract transactions from the HDFC bank statement. ' +
      'Please verify that this is a valid HDFC bank statement and try again.'
    );
  }
  
  // Sort transactions by date
  return transactions.sort((a, b) => {
    const dateA = new Date(a.date.split('/').reverse().join('-'));
    const dateB = new Date(b.date.split('/').reverse().join('-'));
    return dateA.getTime() - dateB.getTime();
  });
};

/**
 * Extract transactions from ICICI bank statement format
 */
export const extractICICITransactions = (textContent: string[]): Transaction[] => {
  const transactions: Transaction[] = [];
  const transactionRegex = /(\d{2}\/\d{2}\/\d{4})\s+([^\d]+?)(?:\s+([\d,]+\.\d{2}))?\s+(?:\s+([\d,]+\.\d{2}))?\s+([\d,]+\.\d{2})/g;
  
  textContent.forEach(pageText => {
    let match;
    while ((match = transactionRegex.exec(pageText)) !== null) {
      const [, date, description, debitStr, creditStr, balanceStr] = match;
      
      // Convert strings to numbers, removing commas
      const debit = debitStr ? parseFloat(debitStr.replace(/,/g, '')) : null;
      const credit = creditStr ? parseFloat(creditStr.replace(/,/g, '')) : null;
      const balance = balanceStr ? parseFloat(balanceStr.replace(/,/g, '')) : null;
      
      transactions.push({
        id: `${date}-${Math.random().toString(36).substring(2, 9)}`,
        date,
        description: description.trim(),
        debit,
        credit,
        balance
      });
    }
  });
  
  return transactions;
};

// Attempt to detect bank format from text content
export const detectBankFormat = (textContent: string[]): string | null => {
  const fullText = textContent.join(' ').toLowerCase();
  
  // Prioritize HDFC detection since user mentioned it's HDFC
  if (fullText.includes('hdfc bank') || fullText.includes('hdfc')) {
    return 'HDFC';
  } else if (fullText.includes('state bank of india') || fullText.includes('sbi')) {
    return 'SBI';
  } else if (fullText.includes('icici bank') || fullText.includes('icici')) {
    return 'ICICI';
  }
  
  return null;
};

// Generic extractor that tries to determine the bank format and extract transactions
export const extractTransactions = async (file: File): Promise<{ transactions: Transaction[], bankName: string | null }> => {
  try {
    const textContent = await extractTextFromPDF(file);
    const bankName = detectBankFormat(textContent);
    
    let transactions: Transaction[] = [];
    
    // Try HDFC format first since user mentioned it's HDFC
    try {
      transactions = extractHDFCTransactions(textContent);
      return { transactions, bankName: 'HDFC' };
    } catch (error) {
      console.debug('HDFC extraction failed, trying other formats:', error);
      
      if (bankName === 'SBI') {
        transactions = extractSBITransactions(textContent);
      } else if (bankName === 'ICICI') {
        transactions = extractICICITransactions(textContent);
      } else {
        // If all specific formats fail, try generic extractor
        transactions = extractGenericTransactions(textContent);
      }
    }

    if (transactions.length === 0) {
      throw new Error(
        bankName 
          ? `Unable to extract transactions from the ${bankName} bank statement. Please verify the statement format.`
          : 'No transactions found. Please try selecting your bank manually.'
      );
    }
    
    return { transactions, bankName };
  } catch (error) {
    console.error('Error extracting transactions:', error);
    throw error instanceof Error ? error : new Error('An unexpected error occurred while processing the PDF');
  }
};

// Generic extractor that tries to identify transaction patterns
const extractGenericTransactions = (textContent: string[]): Transaction[] => {
  const transactions: Transaction[] = [];
  // Enhanced date regex to match more formats
  const dateRegex = /(\d{2}[\/\-\.]\d{2}[\/\-\.]\d{2,4})/;
  const amountRegex = /([\d,]+\.\d{2})/g;
  
  textContent.forEach(pageText => {
    const lines = pageText.split(/\n|\r\n/);
    
    lines.forEach((line, index) => {
      if (dateRegex.test(line)) {
        const dateMatch = line.match(dateRegex);
        if (dateMatch) {
          const date = dateMatch[0];
          const description = line.replace(dateRegex, '').replace(amountRegex, '').trim() || 'Unknown transaction';
          
          const amounts = line.match(amountRegex)?.map(amount => parseFloat(amount.replace(/,/g, ''))) || [];
          
          // Also check the next line for amounts
          if (index < lines.length - 1) {
            const nextLine = lines[index + 1];
            const nextLineAmounts = nextLine.match(amountRegex)?.map(amount => parseFloat(amount.replace(/,/g, ''))) || [];
            amounts.push(...nextLineAmounts);
          }
          
          if (amounts.length > 0) {
            let debit = null;
            let credit = null;
            let balance = amounts[amounts.length - 1]; // Last amount is usually the balance
            
            // Try to determine if amount is debit or credit
            if (amounts.length > 1) {
              const lineText = (line + (index < lines.length - 1 ? lines[index + 1] : '')).toLowerCase();
              if (lineText.includes('dr') || lineText.includes('debit') || lineText.includes('withdrawal')) {
                debit = amounts[0];
              } else if (lineText.includes('cr') || lineText.includes('credit') || lineText.includes('deposit')) {
                credit = amounts[0];
              } else {
                // If no indicator, assume first amount is debit if there's a decrease in balance
                if (amounts.length >= 2 && amounts[0] < amounts[1]) {
                  credit = amounts[0];
                } else {
                  debit = amounts[0];
                }
              }
            }
            
            transactions.push({
              id: `${date}-${Math.random().toString(36).substring(2, 9)}`,
              date,
              description,
              debit,
              credit,
              balance
            });
          }
        }
      }
    });
  });
  
  return transactions;
};