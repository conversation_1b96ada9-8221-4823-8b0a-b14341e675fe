export interface Transaction {
  id: string;
  date: string;
  description: string;
  refNo?: string;
  valueDate?: string;
  debit: number | null;
  credit: number | null;
  balance: number | null;
}

export interface PreviewData {
  transactions: Transaction[];
  bankName: string;
  accountNumber?: string;
  startDate?: string;
  endDate?: string;
}

export interface BankFormat {
  id: string;
  name: string;
  logo?: string;
}

export enum ProcessingStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  ERROR = 'error'
}

export interface ProcessingError {
  message: string;
  details?: string;
}

export enum ConversionStep {
  UPLOAD = 'upload',
  SELECT_BANK = 'select_bank',
  PREVIEW = 'preview',
  DOWNLOAD = 'download'
}