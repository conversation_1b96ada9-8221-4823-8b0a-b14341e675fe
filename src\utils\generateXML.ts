import { Transaction } from '../types';
import { format, parse } from 'date-fns';

/**
 * Convert a date string to Tally format (DD-MM-YYYY)
 */
const formatDateForTally = (dateString: string): string => {
  // Handle common date formats
  let parsedDate: Date;
  
  if (dateString.includes('/')) {
    // Format DD/MM/YYYY or MM/DD/YYYY
    const parts = dateString.split('/');
    if (parts[0].length === 2 && parts[1].length === 2) {
      // Assume DD/MM/YYYY
      parsedDate = parse(dateString, 'dd/MM/yyyy', new Date());
    } else {
      // Assume MM/DD/YYYY
      parsedDate = parse(dateString, 'MM/dd/yyyy', new Date());
    }
  } else if (dateString.includes('-')) {
    // Format YYYY-MM-DD or DD-MM-YYYY
    const parts = dateString.split('-');
    if (parts[0].length === 4) {
      // Assume YYYY-MM-DD
      parsedDate = parse(dateString, 'yyyy-MM-dd', new Date());
    } else {
      // Assume DD-MM-YYYY
      parsedDate = parse(dateString, 'dd-MM-yyyy', new Date());
    }
  } else if (dateString.includes('.')) {
    // Format DD.MM.YYYY
    parsedDate = parse(dateString, 'dd.MM.yyyy', new Date());
  } else {
    // Default fallback
    parsedDate = new Date(dateString);
  }
  
  return format(parsedDate, 'dd-MM-yyyy');
};

/**
 * Generate Tally XML from transaction data
 */
export const generateTallyXML = (
  transactions: Transaction[],
  bankName: string,
  companyName = 'Your Company'
): string => {
  // XML header
  let xml = `<ENVELOPE>
  <HEADER>
    <TALLYREQUEST>Import Data</TALLYREQUEST>
  </HEADER>
  <BODY>
    <IMPORTDATA>
      <REQUESTDESC>
        <REPORTNAME>Vouchers</REPORTNAME>
        <STATICVARIABLES>
          <SVCURRENTCOMPANY>${companyName}</SVCURRENTCOMPANY>
        </STATICVARIABLES>
      </REQUESTDESC>
      <REQUESTDATA>`;
  
  // Generate vouchers for each transaction
  transactions.forEach(transaction => {
    // Skip invalid transactions
    if (!transaction.date) return;

    // Determine if it's a debit or credit transaction
    const isDebit = transaction.debit !== null && transaction.debit > 0;
    const amount = isDebit ? transaction.debit : transaction.credit;
    
    // Skip if no amount
    if (amount === null) return;
    
    const tallyDate = formatDateForTally(transaction.date);
    
    xml += `
        <TALLYMESSAGE xmlns:UDF="TallyUDF">
          <VOUCHER VCHTYPE="Journal" ACTION="Create">
            <DATE>${tallyDate}</DATE>
            <NARRATION>${transaction.description}</NARRATION>
            <VOUCHERTYPENAME>Journal</VOUCHERTYPENAME>
            <ALLLEDGERENTRIES.LIST>
              <LEDGERNAME>${bankName} Bank</LEDGERNAME>
              <ISDEEMEDPOSITIVE>${isDebit ? 'Yes' : 'No'}</ISDEEMEDPOSITIVE>
              <AMOUNT>${isDebit ? `-${amount}` : amount}</AMOUNT>
            </ALLLEDGERENTRIES.LIST>
            <ALLLEDGERENTRIES.LIST>
              <LEDGERNAME>Suspense Account</LEDGERNAME>
              <ISDEEMEDPOSITIVE>${isDebit ? 'No' : 'Yes'}</ISDEEMEDPOSITIVE>
              <AMOUNT>${isDebit ? amount : `-${amount}`}</AMOUNT>
            </ALLLEDGERENTRIES.LIST>
          </VOUCHER>
        </TALLYMESSAGE>`;
  });
  
  // XML footer
  xml += `
      </REQUESTDATA>
    </IMPORTDATA>
  </BODY>
</ENVELOPE>`;
  
  return xml;
};

/**
 * Create a downloadable XML blob
 */
export const createXMLDownload = (xml: string, filename = 'tally_import.xml'): void => {
  const blob = new Blob([xml], { type: 'application/xml' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  
  // Clean up
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 100);
};