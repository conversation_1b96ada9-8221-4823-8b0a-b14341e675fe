import React from 'react';
import { ConversionStep } from '../types';
import { FileUp, CreditCard, Table, Download, CheckCircle } from 'lucide-react';
import classNames from 'classnames';

interface StepIndicatorProps {
  currentStep: ConversionStep;
  completedSteps: ConversionStep[];
}

interface StepConfig {
  key: ConversionStep;
  label: string;
  icon: React.ReactNode;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep, completedSteps }) => {
  const steps: StepConfig[] = [
    { 
      key: ConversionStep.UPLOAD, 
      label: 'Upload Statement', 
      icon: <FileUp className="w-5 h-5" /> 
    },
    { 
      key: ConversionStep.SELECT_BANK, 
      label: 'Select Bank', 
      icon: <CreditCard className="w-5 h-5" /> 
    },
    { 
      key: ConversionStep.PREVIEW, 
      label: 'Preview Data', 
      icon: <Table className="w-5 h-5" /> 
    },
    { 
      key: ConversionStep.DOWNLOAD, 
      label: 'Download XML', 
      icon: <Download className="w-5 h-5" /> 
    }
  ];

  return (
    <div className="w-full px-4 py-5">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.includes(step.key);
          const isCurrent = currentStep === step.key;
          const isDisabled = !isCompleted && !isCurrent;
          
          return (
            <React.Fragment key={step.key}>
              {/* Step bubble */}
              <div className="flex flex-col items-center relative">
                <div 
                  className={classNames(
                    "w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300",
                    {
                      "bg-blue-600 text-white": isCurrent,
                      "bg-green-500 text-white": isCompleted && !isCurrent,
                      "bg-gray-200 text-gray-400": isDisabled
                    }
                  )}
                >
                  {isCompleted && !isCurrent ? <CheckCircle className="w-5 h-5" /> : step.icon}
                </div>
                <span 
                  className={classNames(
                    "mt-2 text-xs font-medium whitespace-nowrap transition-all duration-300",
                    {
                      "text-blue-600": isCurrent,
                      "text-green-500": isCompleted && !isCurrent,
                      "text-gray-400": isDisabled
                    }
                  )}
                >
                  {step.label}
                </span>
              </div>
              
              {/* Connector line (except for last item) */}
              {index < steps.length - 1 && (
                <div 
                  className={classNames(
                    "flex-1 h-1 mx-2 transition-all duration-500",
                    {
                      "bg-green-500": completedSteps.includes(steps[index + 1].key),
                      "bg-gray-200": !completedSteps.includes(steps[index + 1].key)
                    }
                  )}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;