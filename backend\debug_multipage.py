#!/usr/bin/env python3
"""
Debug script for multi-page PDF extraction issues
"""

import sys
import os
import logging
import tabula
import pandas as pd
from pdf_processor import PDFProcessor

# Setup detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def debug_multipage_extraction(pdf_path: str):
    """
    Debug multi-page PDF extraction step by step
    """
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    print(f"🔍 Debugging multi-page extraction for: {pdf_path}")
    print("=" * 80)
    
    try:
        # Get page count first
        try:
            import fitz
            doc = fitz.open(pdf_path)
            page_count = doc.page_count
            doc.close()
            print(f"📖 Total pages in PDF: {page_count}")
        except Exception as e:
            print(f"❌ Could not determine page count: {str(e)}")
            return
        
        # Test Tabula extraction page by page
        print(f"\n🔧 Testing Tabula extraction page by page...")
        all_page_tables = []
        
        for page_num in range(1, page_count + 1):
            print(f"\n--- Page {page_num} ---")
            try:
                # Extract tables from specific page
                page_tables = tabula.read_pdf(
                    pdf_path,
                    pages=page_num,
                    multiple_tables=True,
                    pandas_options={'header': None}
                )
                
                print(f"   📊 Found {len(page_tables)} tables on page {page_num}")
                
                for i, table in enumerate(page_tables):
                    if isinstance(table, pd.DataFrame) and not table.empty:
                        print(f"   Table {i+1}: Shape {table.shape}")
                        print(f"   First few rows:\n{table.head(3)}")
                        all_page_tables.append((page_num, i+1, table))
                    else:
                        print(f"   Table {i+1}: Empty or invalid")
                        
            except Exception as e:
                print(f"   ❌ Error extracting from page {page_num}: {str(e)}")
        
        print(f"\n📊 Total tables found across all pages: {len(all_page_tables)}")
        
        # Test combined extraction
        print(f"\n🔧 Testing combined extraction (pages='all')...")
        try:
            combined_tables = tabula.read_pdf(
                pdf_path,
                pages='all',
                multiple_tables=True,
                pandas_options={'header': None}
            )
            print(f"   📊 Combined extraction found {len(combined_tables)} tables")
            
            for i, table in enumerate(combined_tables):
                if isinstance(table, pd.DataFrame) and not table.empty:
                    print(f"   Combined Table {i+1}: Shape {table.shape}")
                else:
                    print(f"   Combined Table {i+1}: Empty or invalid")
                    
        except Exception as e:
            print(f"   ❌ Combined extraction failed: {str(e)}")
        
        # Test HDFC processor on each table
        print(f"\n🏦 Testing HDFC processor on extracted tables...")
        processor = PDFProcessor()
        total_transactions = 0
        
        for page_num, table_num, table in all_page_tables:
            print(f"\n--- Processing Page {page_num}, Table {table_num} ---")
            try:
                transactions = processor.hdfc_processor.process_hdfc_dataframe(table)
                print(f"   ✅ Extracted {len(transactions)} transactions")
                total_transactions += len(transactions)
                
                if transactions:
                    print(f"   Sample transaction: {transactions[0]}")
                    
            except Exception as e:
                print(f"   ❌ HDFC processing failed: {str(e)}")
        
        print(f"\n📈 Total transactions from all pages: {total_transactions}")
        
        # Test full processing
        print(f"\n🚀 Testing full processing pipeline...")
        try:
            result = processor.process_pdf(pdf_path)
            final_count = len(result.get('transactions', []))
            print(f"   ✅ Final result: {final_count} transactions")
            
            if final_count != total_transactions:
                print(f"   ⚠️  Mismatch: Expected {total_transactions}, got {final_count}")
                print(f"   This might indicate deduplication or filtering")
            
        except Exception as e:
            print(f"   ❌ Full processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # Test different extraction strategies
        print(f"\n🔧 Testing alternative extraction strategies...")
        
        # Strategy 1: Lattice
        try:
            lattice_tables = tabula.read_pdf(
                pdf_path,
                pages='all',
                multiple_tables=True,
                lattice=True,
                pandas_options={'header': None}
            )
            print(f"   Lattice strategy: {len(lattice_tables)} tables")
        except Exception as e:
            print(f"   Lattice strategy failed: {str(e)}")
        
        # Strategy 2: Stream
        try:
            stream_tables = tabula.read_pdf(
                pdf_path,
                pages='all',
                multiple_tables=True,
                stream=True,
                pandas_options={'header': None}
            )
            print(f"   Stream strategy: {len(stream_tables)} tables")
        except Exception as e:
            print(f"   Stream strategy failed: {str(e)}")
        
        # Strategy 3: Guess
        try:
            guess_tables = tabula.read_pdf(
                pdf_path,
                pages='all',
                multiple_tables=True,
                guess=True,
                pandas_options={'header': None}
            )
            print(f"   Guess strategy: {len(guess_tables)} tables")
        except Exception as e:
            print(f"   Guess strategy failed: {str(e)}")
        
    except Exception as e:
        print(f"❌ Debug process failed: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """
    Main function
    """
    if len(sys.argv) != 2:
        print("Usage: python debug_multipage.py <pdf_path>")
        print("Example: python debug_multipage.py uploads/sample_statement.pdf")
        return
    
    pdf_path = sys.argv[1]
    debug_multipage_extraction(pdf_path)

if __name__ == "__main__":
    main()
