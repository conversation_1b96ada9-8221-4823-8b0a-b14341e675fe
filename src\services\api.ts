import { Transaction, BankFormat } from '../types';

const API_BASE_URL = 'http://localhost:5000';

export interface ProcessPDFResponse {
  success: boolean;
  data?: {
    transactions: Transaction[];
    bank_name: string;
    extraction_method: string;
    total_transactions: number;
  };
  message?: string;
  error?: string;
  details?: string;
}

export interface SupportedBanksResponse {
  success: boolean;
  banks: BankFormat[];
}

class APIService {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  /**
   * Check if the API server is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/health`);
      const data = await response.json();
      return data.status === 'healthy';
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Process PDF file and extract transactions
   */
  async processPDF(file: File): Promise<ProcessPDFResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${this.baseURL}/api/process-pdf`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to process PDF');
      }

      return data;
    } catch (error) {
      console.error('PDF processing failed:', error);
      
      if (error instanceof Error) {
        return {
          success: false,
          error: 'Processing failed',
          message: error.message,
          details: error.message
        };
      }

      return {
        success: false,
        error: 'Unknown error',
        message: 'An unexpected error occurred while processing the PDF'
      };
    }
  }

  /**
   * Get list of supported banks
   */
  async getSupportedBanks(): Promise<SupportedBanksResponse> {
    try {
      const response = await fetch(`${this.baseURL}/api/supported-banks`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error('Failed to fetch supported banks');
      }

      return data;
    } catch (error) {
      console.error('Failed to fetch supported banks:', error);
      
      // Return default banks as fallback
      return {
        success: false,
        banks: [
          { id: 'SBI', name: 'State Bank of India' },
          { id: 'HDFC', name: 'HDFC Bank' },
          { id: 'ICICI', name: 'ICICI Bank' },
          { id: 'AXIS', name: 'Axis Bank' },
          { id: 'KOTAK', name: 'Kotak Mahindra Bank' },
          { id: 'PNB', name: 'Punjab National Bank' },
          { id: 'BOB', name: 'Bank of Baroda' },
          { id: 'CANARA', name: 'Canara Bank' },
          { id: 'UNION', name: 'Union Bank of India' },
          { id: 'GENERIC', name: 'Other Banks (Generic Format)' }
        ]
      };
    }
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (file.type !== 'application/pdf') {
      return {
        valid: false,
        error: 'Only PDF files are allowed'
      };
    }

    // Check file size (16MB limit)
    const maxSize = 16 * 1024 * 1024; // 16MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size exceeds the maximum limit of 16MB'
      };
    }

    // Check if file is empty
    if (file.size === 0) {
      return {
        valid: false,
        error: 'File is empty'
      };
    }

    return { valid: true };
  }

  /**
   * Transform API response to frontend format (HDFC-specific)
   */
  transformTransactions(apiTransactions: any[]): Transaction[] {
    return apiTransactions.map((txn, index) => ({
      id: txn.id || `txn_${Date.now()}_${index}`,
      date: txn.date,
      description: txn.narration || txn.description || '',
      refNo: txn.chq_ref_no || txn.refNo || undefined,
      valueDate: txn.value_date || txn.valueDate || undefined,
      debit: txn.withdrawal_amt || txn.debit || null,
      credit: txn.deposit_amt || txn.credit || null,
      balance: txn.closing_balance || txn.balance || null
    }));
  }
}

// Create and export a singleton instance
export const apiService = new APIService();

// Export the class for testing or custom instances
export default APIService;
