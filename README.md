# Bank Statement to Tally XML Converter

An advanced PDF processing application that converts bank statements into Tally-compatible XML format using state-of-the-art table extraction and OCR technology.

## 🚀 Features

### Advanced PDF Processing
- **Table Extraction**: Uses Tabula-py and Camelot-py for extracting tables from regular PDFs
- **OCR Support**: Processes scanned/image-based PDFs using Tesseract OCR with image preprocessing
- **Multi-Bank Support**: Supports major Indian banks (SBI, HDFC, ICICI, Axis, Kotak, PNB, BOB, Canara, Union)
- **Smart Detection**: Automatically detects bank format and PDF type (regular vs scanned)

### User Experience
- **Clean UI**: Professional interface similar to repotic.in with intuitive design
- **Real-time Preview**: Review and edit extracted transactions before XML generation
- **Progress Tracking**: Step-by-step process with clear indicators
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **API Status**: Real-time backend API health monitoring

### Technical Architecture
- **Frontend**: React + TypeScript with Vite for fast development
- **Backend**: Python Flask API with CORS support
- **Processing**: Pandas for data manipulation and structuring
- **Validation**: File type and size validation with detailed feedback

## 📋 Prerequisites

### System Requirements
- **Python 3.8+** (for backend processing)
- **Node.js 16+** (for frontend development)
- **Java JRE 11+** (required for Tabula table extraction)
- **Tesseract OCR** (for scanned PDF processing)

### Platform-Specific Setup

#### Windows
1. Install Python from [python.org](https://python.org)
2. Install Node.js from [nodejs.org](https://nodejs.org)
3. Install Java JRE from [Oracle](https://www.oracle.com/java/technologies/downloads/) or [OpenJDK](https://openjdk.org/)
4. Install Tesseract from [GitHub](https://github.com/UB-Mannheim/tesseract/wiki)
5. Add Tesseract to your PATH environment variable

#### macOS
```bash
# Install using Homebrew
brew install python node openjdk@11 tesseract
```

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install -y python3 python3-pip nodejs npm openjdk-11-jre-headless tesseract-ocr
```

## 🛠️ Installation

### Quick Start (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bank-to-tally-converter
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Setup backend**
   ```bash
   cd backend
   python setup.py
   ```

4. **Start both servers**
   
   **Windows:**
   ```bash
   start.bat
   ```
   
   **macOS/Linux:**
   ```bash
   ./start.sh
   ```

### Manual Installation

#### Frontend Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

#### Backend Setup
```bash
# Navigate to backend directory
cd backend

# Install Python dependencies
pip install -r requirements.txt

# Start the API server
python app.py
```

## 🎯 Usage

1. **Start the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000

2. **Upload Bank Statement**
   - Drag and drop or select your PDF bank statement
   - Supports both regular and scanned PDFs
   - Maximum file size: 16MB

3. **Review Extracted Data**
   - Preview extracted transactions in a clean table format
   - Edit any incorrect data directly in the preview
   - Verify dates, amounts, and descriptions

4. **Generate Tally XML**
   - Click "Generate XML" to create Tally-compatible file
   - Download automatically starts
   - Import the XML file into Tally

## 🏗️ Architecture

### Frontend (React + TypeScript)
```
src/
├── components/          # React components
│   ├── FileUpload.tsx   # File upload with drag-drop
│   ├── TransactionPreview.tsx  # Data preview table
│   ├── BankSelector.tsx # Bank format selection
│   └── ...
├── services/           # API communication
│   └── api.ts         # Backend API service
├── types/             # TypeScript definitions
└── utils/             # Utility functions
```

### Backend (Python Flask)
```
backend/
├── app.py             # Flask application
├── pdf_processor.py   # Core PDF processing logic
├── requirements.txt   # Python dependencies
└── setup.py          # Installation script
```

## 🔧 API Endpoints

### Health Check
```
GET /health
```

### Process PDF
```
POST /api/process-pdf
Content-Type: multipart/form-data
Body: PDF file with key 'file'
```

### Supported Banks
```
GET /api/supported-banks
```

## 🎨 UI Features

### Transaction Preview Table
- **Clean Design**: Professional table layout with proper spacing
- **Color Coding**: Visual indicators for debits (red) and credits (green)
- **Inline Editing**: Click to edit any transaction data
- **Sorting**: Automatic date-based sorting
- **Validation**: Real-time validation with error highlighting
- **Responsive**: Works on desktop and mobile devices

### Status Indicators
- **API Health**: Real-time backend connectivity status
- **Processing Status**: Loading states and progress indicators
- **Error Messages**: Clear, actionable error messages

## 🔍 Supported Banks

- State Bank of India (SBI)
- HDFC Bank
- ICICI Bank
- Axis Bank
- Kotak Mahindra Bank
- Punjab National Bank (PNB)
- Bank of Baroda (BOB)
- Canara Bank
- Union Bank of India
- Generic format for other banks

## 🛡️ Error Handling

### Common Issues and Solutions

1. **API Offline Error**
   - Ensure backend server is running: `python backend/app.py`
   - Check if port 5000 is available

2. **Java Not Found**
   - Install Java JRE 11+ and add to PATH
   - Verify with: `java -version`

3. **Tesseract Not Found**
   - Install Tesseract OCR and add to PATH
   - Verify with: `tesseract --version`

4. **PDF Processing Failed**
   - Ensure PDF is a valid bank statement
   - Try with a different bank format selection
   - Check if PDF is password protected

## 🚀 Development

### Running in Development Mode

```bash
# Frontend (with hot reload)
npm run dev

# Backend (with debug mode)
cd backend
python app.py
```

### Building for Production

```bash
# Build frontend
npm run build

# Preview production build
npm run preview
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Ensure all prerequisites are installed
3. Verify API connectivity
4. Check browser console for errors

For additional support, please create an issue in the repository.

## 🙏 Acknowledgments

- **Tabula-py**: For PDF table extraction
- **Camelot-py**: For advanced table detection
- **Tesseract**: For OCR capabilities
- **React**: For the user interface
- **Flask**: For the backend API
