#!/usr/bin/env python3
"""
Comprehensive test for enhanced multi-page HDFC PDF processing
Tests the new page-by-page extraction and multi-page processing capabilities
"""

import sys
import os
import logging
import traceback
from typing import List, Dict, Any
import pandas as pd

# Add backend directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pdf_processor import PDFProcessor
    from hdfc_processor import HDFCBankProcessor
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_hdfc_processor_initialization():
    """Test HDFC processor initialization"""
    print("\n🧪 Testing HDFC Processor Initialization...")
    
    try:
        processor = HDFCBankProcessor()
        assert hasattr(processor, 'process_hdfc_dataframe_pages'), "Missing multi-page processing method"
        assert hasattr(processor, '_analyze_page_structures'), "Missing page structure analysis"
        assert hasattr(processor, '_intelligent_deduplication'), "Missing intelligent deduplication"
        print("✅ HDFC processor initialized with all enhanced methods")
        return True
    except Exception as e:
        print(f"❌ HDFC processor initialization failed: {e}")
        return False

def test_pdf_processor_enhanced_method():
    """Test PDF processor enhanced method"""
    print("\n🧪 Testing PDF Processor Enhanced Method...")
    
    try:
        processor = PDFProcessor()
        assert hasattr(processor, '_process_hdfc_pdf_enhanced'), "Missing enhanced HDFC processing method"
        assert hasattr(processor, '_extract_hdfc_page_by_page'), "Missing page-by-page extraction method"
        print("✅ PDF processor has all enhanced methods")
        return True
    except Exception as e:
        print(f"❌ PDF processor enhanced method test failed: {e}")
        return False

def test_multi_page_structure_analysis():
    """Test multi-page structure analysis"""
    print("\n🧪 Testing Multi-Page Structure Analysis...")
    
    try:
        processor = HDFCBankProcessor()
        
        # Create mock tables representing different page types
        # Page 1: Standard HDFC page with headers
        page1_data = {
            0: ['Date', 'Narration', 'Chq./Ref.No.', 'Value Dt', 'Withdrawal Amt.', 'Deposit Amt.', 'Closing Balance'],
            1: ['01/01/24', 'SALARY CREDIT', 'SAL001', '01/01/24', '', '50000.00', '50000.00'],
            2: ['02/01/24', 'ATM WITHDRAWAL', 'ATM001', '02/01/24', '5000.00', '', '45000.00']
        }
        page1_df = pd.DataFrame(page1_data).T
        
        # Page 2: Continuation page (no headers)
        page2_data = {
            0: ['03/01/24', 'ONLINE TRANSFER', 'TXN001', '03/01/24', '10000.00', '', '35000.00'],
            1: ['04/01/24', 'INTEREST CREDIT', 'INT001', '04/01/24', '', '500.00', '35500.00']
        }
        page2_df = pd.DataFrame(page2_data).T
        
        tables = [page1_df, page2_df]
        
        # Test structure analysis
        structures = processor._analyze_page_structures(tables)
        
        assert len(structures) == 2, f"Expected 2 structures, got {len(structures)}"
        assert structures[0]['type'] == 'standard_hdfc', f"Page 1 should be standard_hdfc, got {structures[0]['type']}"
        assert structures[1]['type'] == 'continuation', f"Page 2 should be continuation, got {structures[1]['type']}"
        
        print("✅ Multi-page structure analysis working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Multi-page structure analysis failed: {e}")
        print(traceback.format_exc())
        return False

def test_transaction_format_compatibility():
    """Test transaction format compatibility with frontend"""
    print("\n🧪 Testing Transaction Format Compatibility...")
    
    try:
        processor = HDFCBankProcessor()
        
        # Create a mock row
        mock_row = pd.Series(['01/01/24', 'TEST TRANSACTION', 'REF001', '01/01/24', '1000.00', '', '49000.00'])
        column_mapping = {
            'date': 0,
            'narration': 1,
            'chq_ref_no': 2,
            'value_date': 3,
            'withdrawal': 4,
            'deposit': 5,
            'balance': 6
        }
        
        transaction = processor._extract_transaction_from_row(mock_row, column_mapping, "test_1")
        
        # Check frontend-compatible fields
        required_fields = ['id', 'date', 'description', 'refNo', 'valueDate', 'debit', 'credit', 'balance']
        for field in required_fields:
            assert field in transaction, f"Missing required field: {field}"
        
        # Check backward compatibility fields
        legacy_fields = ['narration', 'chq_ref_no', 'value_date', 'withdrawal_amt', 'deposit_amt', 'closing_balance']
        for field in legacy_fields:
            assert field in transaction, f"Missing legacy field: {field}"
        
        print("✅ Transaction format is compatible with frontend and maintains backward compatibility")
        return True
        
    except Exception as e:
        print(f"❌ Transaction format compatibility test failed: {e}")
        print(traceback.format_exc())
        return False

def test_intelligent_deduplication():
    """Test intelligent deduplication"""
    print("\n🧪 Testing Intelligent Deduplication...")
    
    try:
        processor = HDFCBankProcessor()
        
        # Create test transactions with some duplicates
        transactions = [
            {
                'id': 'txn1',
                'date': '2024-01-01',
                'description': 'SALARY CREDIT',
                'debit': None,
                'credit': 50000.00,
                'balance': 50000.00,
                'source_page': 1
            },
            {
                'id': 'txn2',
                'date': '2024-01-01',
                'description': 'SALARY CREDIT',  # Duplicate
                'debit': None,
                'credit': 50000.00,
                'balance': 50000.00,
                'source_page': 2
            },
            {
                'id': 'txn3',
                'date': '2024-01-02',
                'description': 'ATM WITHDRAWAL',
                'debit': 5000.00,
                'credit': None,
                'balance': 45000.00,
                'source_page': 1
            }
        ]
        
        unique_transactions = processor._intelligent_deduplication(transactions)
        
        assert len(unique_transactions) == 2, f"Expected 2 unique transactions, got {len(unique_transactions)}"
        
        print("✅ Intelligent deduplication working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Intelligent deduplication test failed: {e}")
        print(traceback.format_exc())
        return False

def test_enhanced_date_parsing():
    """Test enhanced date parsing"""
    print("\n🧪 Testing Enhanced Date Parsing...")
    
    try:
        processor = HDFCBankProcessor()
        
        test_dates = [
            ('01/01/24', '2024-01-01'),
            ('01-01-2024', '2024-01-01'),
            ('01.01.24', '2024-01-01'),
            ('1 Jan 2024', '2024-01-01'),
            ('invalid', None),
            ('', None),
            ('Date', None)
        ]
        
        for input_date, expected in test_dates:
            result = processor._parse_hdfc_date(input_date)
            assert result == expected, f"Date parsing failed for {input_date}: expected {expected}, got {result}"
        
        print("✅ Enhanced date parsing working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced date parsing test failed: {e}")
        print(traceback.format_exc())
        return False

def test_enhanced_amount_parsing():
    """Test enhanced amount parsing"""
    print("\n🧪 Testing Enhanced Amount Parsing...")
    
    try:
        processor = HDFCBankProcessor()
        
        test_amounts = [
            ('1,23,456.78', 123456.78),
            ('50000.00', 50000.00),
            ('(1000.00)', -1000.00),  # Negative in parentheses
            ('1,000', 1000.00),
            ('', None),
            ('Amount', None),
            ('invalid', None),
            ('0.00', None)  # Zero amounts should be None
        ]
        
        for input_amount, expected in test_amounts:
            result = processor._parse_hdfc_amount(input_amount)
            if expected is None:
                assert result is None, f"Amount parsing failed for {input_amount}: expected None, got {result}"
            else:
                assert abs(result - expected) < 0.01, f"Amount parsing failed for {input_amount}: expected {expected}, got {result}"
        
        print("✅ Enhanced amount parsing working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced amount parsing test failed: {e}")
        print(traceback.format_exc())
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Enhanced Multi-Page HDFC Processing Tests")
    print("=" * 60)
    
    tests = [
        test_hdfc_processor_initialization,
        test_pdf_processor_enhanced_method,
        test_multi_page_structure_analysis,
        test_transaction_format_compatibility,
        test_intelligent_deduplication,
        test_enhanced_date_parsing,
        test_enhanced_amount_parsing
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Enhanced multi-page HDFC processing is ready!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
