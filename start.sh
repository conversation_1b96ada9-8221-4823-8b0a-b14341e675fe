#!/bin/bash

echo "========================================"
echo "Bank Statement to Tally XML Converter"
echo "========================================"
echo

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if Python is installed
if ! command_exists python3 && ! command_exists python; then
    echo "❌ Python is not installed. Please install Python 3.8+ first."
    exit 1
fi

# Check if Node.js is installed
if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command_exists npm; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo

# Start backend in background
echo "🚀 Starting Backend API Server..."
cd backend
if [ -f "app.py" ]; then
    if command_exists python3; then
        python3 app.py &
    else
        python app.py &
    fi
    BACKEND_PID=$!
    echo "   Backend started with PID: $BACKEND_PID"
else
    echo "❌ Backend app.py not found!"
    exit 1
fi

# Go back to root directory
cd ..

# Wait a moment for backend to start
echo "⏳ Waiting for backend to initialize..."
sleep 3

# Start frontend
echo "🚀 Starting Frontend Development Server..."
if [ -f "package.json" ]; then
    npm run dev &
    FRONTEND_PID=$!
    echo "   Frontend started with PID: $FRONTEND_PID"
else
    echo "❌ Frontend package.json not found!"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo
echo "========================================"
echo "🎉 Both servers are running!"
echo
echo "📡 Backend API: http://localhost:5000"
echo "🌐 Frontend: http://localhost:5173"
echo
echo "Press Ctrl+C to stop both servers"
echo "========================================"

# Function to cleanup on exit
cleanup() {
    echo
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Servers stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup INT TERM

# Wait for user to stop
wait
