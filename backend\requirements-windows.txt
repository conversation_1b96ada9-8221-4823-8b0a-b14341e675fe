# Windows-specific requirements with pre-compiled wheels
# Use this file if you encounter compilation errors with requirements.txt

# Flask and API dependencies
Flask>=2.3.0
Flask-CORS>=4.0.0
Werkzeug>=2.3.0

# PDF processing libraries
tabula-py>=2.8.0

# Use lighter version of camelot without cv2 extras to avoid compilation issues
camelot-py[base]>=0.11.0

# Use fitz (PyMuPDF) - try to get pre-compiled wheel
fitz>=0.0.1.dev2

# If fitz fails, try PyMuPDF with --only-binary flag
# PyMuPDF>=1.23.0

# OCR dependencies
pytesseract>=0.3.10
Pillow>=10.0.0

# OpenCV - use headless version to avoid GUI dependencies
opencv-python-headless>=4.8.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# Additional utilities
python-dateutil>=2.8.0
requests>=2.31.0

# Alternative PDF libraries as fallbacks
pdfplumber>=0.9.0
PyPDF2>=3.0.0
