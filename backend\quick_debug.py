#!/usr/bin/env python3
"""
Quick debug script for PDF processing issues
"""

import tabula
import pandas as pd
import sys
import os

def quick_debug(pdf_path):
    """Quick debug of PDF extraction"""
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return
    
    print(f"🔍 Quick debug for: {pdf_path}")
    print(f"📁 File size: {os.path.getsize(pdf_path)} bytes")
    
    try:
        # Basic Tabula extraction
        print("\n📊 Extracting tables with Tabula...")
        tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)
        
        print(f"✅ Found {len(tables)} tables")
        
        for i, table in enumerate(tables):
            print(f"\n📋 Table {i+1}:")
            print(f"   Shape: {table.shape}")
            
            if table.shape[0] > 0:
                print(f"   First row: {list(table.iloc[0])}")
                
                # Look for date-like patterns
                date_cols = []
                amount_cols = []
                
                for col_idx, col in enumerate(table.columns):
                    col_data = table[col].astype(str)
                    
                    # Check for dates
                    date_count = 0
                    amount_count = 0
                    
                    for val in col_data.head(5):
                        if pd.notna(val) and str(val) != 'nan':
                            # Simple date check
                            if '/' in str(val) or '-' in str(val):
                                try:
                                    parts = str(val).replace('-', '/').split('/')
                                    if len(parts) >= 2 and parts[0].isdigit() and parts[1].isdigit():
                                        date_count += 1
                                except:
                                    pass
                            
                            # Simple amount check
                            try:
                                float(str(val).replace(',', '').replace('Rs.', '').replace('₹', '').strip())
                                amount_count += 1
                            except:
                                pass
                    
                    if date_count >= 2:
                        date_cols.append(col_idx)
                    if amount_count >= 2:
                        amount_cols.append(col_idx)
                
                print(f"   Potential date columns: {date_cols}")
                print(f"   Potential amount columns: {amount_cols}")
                
                # Show sample data
                if table.shape[0] > 1:
                    print(f"   Sample rows:")
                    for row_idx in range(min(3, table.shape[0])):
                        print(f"     Row {row_idx}: {list(table.iloc[row_idx])}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        print(f"📋 Traceback:\n{traceback.format_exc()}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python quick_debug.py <pdf_path>")
        print("Example: python quick_debug.py uploads/statement.pdf")
    else:
        quick_debug(sys.argv[1])
