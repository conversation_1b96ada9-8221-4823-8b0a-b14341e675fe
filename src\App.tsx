import { useState, useEffect } from 'react';
import { FileUp, AlertCircle as CircleAlert, DatabaseBackup } from 'lucide-react';
import FileUpload from './components/FileUpload';
import BankSelector from './components/BankSelector';
import TransactionPreview from './components/TransactionPreview';
import StepIndicator from './components/StepIndicator';
import SuccessDownload from './components/SuccessDownload';
import { BANK_FORMATS } from './constants/bankFormats';
import { ProcessingStatus, ConversionStep, PreviewData, ProcessingError } from './types';
import { apiService } from './services/api';
import { generateTallyXML, createXMLDownload } from './utils/generateXML';

function App() {
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>(ProcessingStatus.IDLE);
  const [error, setError] = useState<ProcessingError | null>(null);
  const [currentStep, setCurrentStep] = useState<ConversionStep>(ConversionStep.UPLOAD);
  const [completedSteps, setCompletedSteps] = useState<ConversionStep[]>([]);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [selectedBank, setSelectedBank] = useState<string | null>(null);
  const [detectedBank, setDetectedBank] = useState<string | null>(null);
  const [xmlContent, setXmlContent] = useState<string | null>(null);
  const [xmlFilename, setXmlFilename] = useState<string>('tally_import.xml');
  const [apiHealthy, setApiHealthy] = useState<boolean | null>(null);
  const [detectionConfidence, setDetectionConfidence] = useState<number>(0);
  const [bankMetadata, setBankMetadata] = useState<any>(null);

  // Check API health on component mount
  useEffect(() => {
    const checkApiHealth = async () => {
      try {
        const healthy = await apiService.healthCheck();
        setApiHealthy(healthy);
      } catch (error) {
        console.error('API health check failed:', error);
        setApiHealthy(false);
      }
    };

    checkApiHealth();
  }, []);

  const handleFileSelect = async (selectedFile: File) => {
    setProcessingStatus(ProcessingStatus.PROCESSING);
    setError(null);

    try {
      // Validate file first
      const validation = apiService.validateFile(selectedFile);
      if (!validation.valid) {
        throw new Error(validation.error || 'Invalid file');
      }

      // Process PDF using the new API
      const response = await apiService.processPDF(selectedFile);

      if (!response.success || !response.data) {
        throw new Error(response.message || response.error || 'Failed to process PDF');
      }

      const { transactions, bank_name, bank_metadata, detection_confidence } = response.data;

      if (!transactions || transactions.length === 0) {
        throw new Error('No transactions could be extracted from the statement. Please check if the file is a valid bank statement.');
      }

      // Transform API response to frontend format
      const transformedTransactions = apiService.transformTransactions(transactions);

      setPreviewData({
        transactions: transformedTransactions,
        bankName: bank_name || 'Unknown',
      });

      // Set HDFC metadata
      setDetectionConfidence(detection_confidence || 0);
      setBankMetadata(bank_metadata || null);

      // Only set the detected bank if we got a valid bank name
      if (bank_name && BANK_FORMATS.some(bank => bank.id === bank_name)) {
        setDetectedBank(bank_name);
        setSelectedBank(bank_name);
      } else {
        // If bank not detected, leave it for manual selection
        setDetectedBank(null);
        setSelectedBank(null);
      }

      setProcessingStatus(ProcessingStatus.SUCCESS);
      setCompletedSteps([...completedSteps, ConversionStep.UPLOAD]);
      setCurrentStep(ConversionStep.SELECT_BANK);
    } catch (err) {
      console.error('Error processing PDF:', err);
      setProcessingStatus(ProcessingStatus.ERROR);
      setError({
        message: err instanceof Error ? err.message : 'Unable to process the bank statement. Please try again or contact support if the issue persists.',
        details: err instanceof Error ? err.message : 'Unknown error'
      });
    }
  };
  
  const handleBankSelect = (bankId: string) => {
    setSelectedBank(bankId);
    setCompletedSteps([...completedSteps, ConversionStep.SELECT_BANK]);
    setCurrentStep(ConversionStep.PREVIEW);
  };
  
  const handleGenerateXML = () => {
    if (!previewData || !selectedBank) return;
    
    const bankName = BANK_FORMATS.find(bank => bank.id === selectedBank)?.name || selectedBank;
    const xml = generateTallyXML(previewData.transactions, bankName);
    
    setXmlContent(xml);
    setXmlFilename(`tally_import_${selectedBank.toLowerCase()}_${new Date().toISOString().slice(0, 10)}.xml`);
    
    createXMLDownload(xml, xmlFilename);
    
    setCompletedSteps([...completedSteps, ConversionStep.PREVIEW, ConversionStep.DOWNLOAD]);
    setCurrentStep(ConversionStep.DOWNLOAD);
  };
  
  const handleDownloadAgain = () => {
    if (xmlContent) {
      createXMLDownload(xmlContent, xmlFilename);
    }
  };
  
  const handleReset = () => {
    setProcessingStatus(ProcessingStatus.IDLE);
    setError(null);
    setCurrentStep(ConversionStep.UPLOAD);
    setCompletedSteps([]);
    setPreviewData(null);
    setSelectedBank(null);
    setDetectedBank(null);
    setXmlContent(null);
    setDetectionConfidence(0);
    setBankMetadata(null);
  };
  
  // Update document title
  useEffect(() => {
    document.title = 'Bank to Tally Converter';
    
    // Get the default title element
    const titleElement = document.querySelector('title[data-default]');
    if (titleElement) {
      titleElement.textContent = 'Bank to Tally Converter';
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container-custom py-4 flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-lg bg-blue-600 flex items-center justify-center mr-3">
              <DatabaseBackup className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Bank to Tally</h1>
              <p className="text-sm text-gray-500">Convert bank statements to Tally XML</p>
            </div>
          </div>

          {/* API Status Indicator */}
          <div className="flex items-center">
            {apiHealthy === null ? (
              <div className="flex items-center text-gray-500">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-500 mr-2"></div>
                <span className="text-xs">Checking API...</span>
              </div>
            ) : apiHealthy ? (
              <div className="flex items-center text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-xs">API Online</span>
              </div>
            ) : (
              <div className="flex items-center text-red-600">
                <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                <span className="text-xs">API Offline</span>
              </div>
            )}
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <main className="container-custom py-8">
        <StepIndicator 
          currentStep={currentStep} 
          completedSteps={completedSteps} 
        />
        
        <div className="mt-8">
          {currentStep === ConversionStep.UPLOAD && (
            <div className="max-w-3xl mx-auto">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Upload Your Bank Statement
                </h2>
                <p className="text-gray-600">
                  Select or drag and drop your HDFC Bank PDF statement below.
                  The system will automatically detect and process HDFC Bank statements.
                </p>
              </div>
              
              <FileUpload 
                onFileSelect={handleFileSelect} 
                status={processingStatus}
                error={error?.message}
              />
              
              <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <CircleAlert className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Advanced PDF Processing
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>
                        Uses advanced table extraction (Tabula, Camelot) and OCR technology for both regular and scanned PDFs.
                        Files are processed securely and temporarily stored only during processing.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {!apiHealthy && (
                <div className="mt-4 p-4 bg-red-50 rounded-lg">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <CircleAlert className="h-5 w-5 text-red-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Backend API Required
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          The processing API is not available. Please start the backend server by running:
                        </p>
                        <code className="block mt-2 p-2 bg-red-100 rounded text-xs">
                          cd backend && python app.py
                        </code>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          
          {currentStep === ConversionStep.SELECT_BANK && selectedBank !== null && (
            <div className="max-w-3xl mx-auto">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Select Your Bank
                </h2>
                <p className="text-gray-600">
                  We've detected your bank statement format. Please confirm or select a different bank.
                </p>
              </div>
              
              <BankSelector
                banks={BANK_FORMATS}
                selectedBank={selectedBank}
                onBankSelect={handleBankSelect}
                detectedBank={detectedBank}
              />
              
              <div className="mt-6 flex justify-between">
                <button 
                  className="btn btn-secondary"
                  onClick={() => {
                    setCurrentStep(ConversionStep.UPLOAD);
                    setCompletedSteps(completedSteps.filter(step => step !== ConversionStep.UPLOAD));
                  }}
                >
                  Back
                </button>
                
                <button 
                  className="btn btn-primary"
                  onClick={() => handleBankSelect(selectedBank)}
                >
                  Continue
                </button>
              </div>
            </div>
          )}
          
          {currentStep === ConversionStep.PREVIEW && previewData && (
            <div>
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Preview Transactions
                </h2>
                <p className="text-gray-600">
                  Review the extracted transactions before generating the Tally XML file.
                </p>
              </div>
              
              <TransactionPreview
                transactions={previewData.transactions}
                onGenerateXML={handleGenerateXML}
                bankName={BANK_FORMATS.find(bank => bank.id === selectedBank)?.name || previewData.bankName}
                onTransactionsChange={(updatedTransactions) => {
                  setPreviewData({
                    ...previewData,
                    transactions: updatedTransactions
                  });
                }}
                detectionConfidence={detectionConfidence}
                bankMetadata={bankMetadata}
              />
              
              <div className="mt-6 flex justify-start">
                <button 
                  className="btn btn-secondary"
                  onClick={() => {
                    setCurrentStep(ConversionStep.SELECT_BANK);
                    setCompletedSteps(completedSteps.filter(step => 
                      step !== ConversionStep.SELECT_BANK && step !== ConversionStep.PREVIEW
                    ));
                  }}
                >
                  Back
                </button>
              </div>
            </div>
          )}
          
          {currentStep === ConversionStep.DOWNLOAD && (
            <div>
              <SuccessDownload 
                onDownloadAgain={handleDownloadAgain}
                onReset={handleReset}
                filename={xmlFilename}
              />
            </div>
          )}
        </div>
      </main>
      
      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-auto">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-500">
              &copy; {new Date().getFullYear()} Bank to Tally Converter. All rights reserved.
            </p>
            <div className="flex items-center mt-4 md:mt-0">
              <span className="text-sm text-gray-500 mr-2">Powered by</span>
              <FileUp className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-gray-500 ml-2">
                Advanced PDF processing with Tabula, Camelot & OCR
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;