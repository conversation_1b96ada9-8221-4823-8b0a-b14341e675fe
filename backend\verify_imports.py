#!/usr/bin/env python3
"""
Verify all imports are working correctly
"""

def test_pymupdf():
    """Test PyMuPDF import specifically"""
    try:
        import fitz  # This is the correct way to import PyMuPDF
        print("✅ PyMuPDF imported successfully as 'fitz'")
        print(f"   Version: {fitz.version}")
        return True
    except ImportError as e:
        print(f"❌ PyMuPDF import failed: {e}")
        return False

def test_all_imports():
    """Test all required imports"""
    imports_to_test = [
        ("flask", "Flask"),
        ("pandas", "Pandas"),
        ("tabula", "Tabula-py"),
        ("camelot", "Camelot-py"),
        ("pytesseract", "PyTesseract"),
        ("cv2", "OpenCV"),
        ("fitz", "PyMuPDF"),
        ("PIL", "Pillow")
    ]
    
    print("Testing all imports:")
    all_passed = True
    
    for module, name in imports_to_test:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")
            all_passed = False
    
    return all_passed

def test_pdf_processor():
    """Test PDF processor import"""
    try:
        from pdf_processor import PDFProcessor
        processor = PDFProcessor()
        print("✅ PDF Processor imported and initialized successfully")
        return True
    except ImportError as e:
        print(f"❌ PDF Processor import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ PDF Processor initialization failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Verifying all imports...\n")
    
    # Test PyMuPDF specifically
    pymupdf_ok = test_pymupdf()
    print()
    
    # Test all imports
    all_imports_ok = test_all_imports()
    print()
    
    # Test PDF processor
    processor_ok = test_pdf_processor()
    print()
    
    if pymupdf_ok and all_imports_ok and processor_ok:
        print("🎉 All imports verified successfully!")
        print("The 'Import PyMuPDF could not be resolved' issue is fixed.")
    else:
        print("⚠️ Some imports failed. Please check the errors above.")
