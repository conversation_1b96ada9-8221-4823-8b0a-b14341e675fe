#!/usr/bin/env python3
"""
Simple test script to check what Tabula can extract from a PDF
"""

import tabula
import pandas as pd
import sys
import os

def test_tabula_extraction(pdf_path):
    """Test basic Tabula extraction"""
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    print(f"🔍 Testing Tabula extraction on: {pdf_path}")
    print(f"📁 File size: {os.path.getsize(pdf_path)} bytes")
    
    try:
        # Try basic extraction
        print("\n📊 Attempting basic table extraction...")
        tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)
        
        print(f"✅ Found {len(tables)} tables")
        
        for i, table in enumerate(tables):
            print(f"\n📋 Table {i+1}:")
            print(f"   Shape: {table.shape}")
            print(f"   Columns: {list(table.columns)}")
            print(f"   Sample data:")
            print(table.head().to_string())
            print("-" * 50)
        
        # Try with different options
        print("\n🔧 Trying with lattice method...")
        try:
            tables_lattice = tabula.read_pdf(pdf_path, pages='all', lattice=True, multiple_tables=True)
            print(f"✅ Lattice method found {len(tables_lattice)} tables")
        except Exception as e:
            print(f"⚠️ Lattice method failed: {e}")
        
        # Try with stream method
        print("\n🔧 Trying with stream method...")
        try:
            tables_stream = tabula.read_pdf(pdf_path, pages='all', stream=True, multiple_tables=True)
            print(f"✅ Stream method found {len(tables_stream)} tables")
        except Exception as e:
            print(f"⚠️ Stream method failed: {e}")
            
    except Exception as e:
        print(f"❌ Tabula extraction failed: {str(e)}")
        import traceback
        print(f"📋 Full traceback:\n{traceback.format_exc()}")

def main():
    """Main function"""
    
    if len(sys.argv) < 2:
        print("Usage: python test_tabula.py <path_to_pdf>")
        print("\nExample:")
        print("python test_tabula.py uploads/statement.pdf")
        return
    
    pdf_path = sys.argv[1]
    test_tabula_extraction(pdf_path)

if __name__ == "__main__":
    main()
