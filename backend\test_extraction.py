#!/usr/bin/env python3
"""
Test script to verify the improved extraction logic
"""

import pandas as pd
from pdf_processor import PDFProcessor
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)

def test_sample_data():
    """Test with sample bank statement data"""
    
    # Create sample data that mimics SBI bank statement
    sample_data = {
        0: ['Date', '01/01/2024', '02/01/2024', '03/01/2024', '04/01/2024'],
        1: ['Description', 'ATM WITHDRAWAL', 'SALARY CREDIT', 'ONLINE TRANSFER', 'UPI PAYMENT'],
        2: ['Ref No', 'REF001', 'REF002', 'REF003', 'REF004'],
        3: ['Debit', '5000.00', '', '2000.00', '500.00'],
        4: ['Credit', '', '50000.00', '', ''],
        5: ['Balance', '45000.00', '95000.00', '93000.00', '92500.00']
    }
    
    df = pd.DataFrame(sample_data)
    print("Sample DataFrame:")
    print(df)
    print()
    
    # Test the processor
    processor = PDFProcessor()
    transactions = processor._process_dataframe_to_transactions(df)
    
    print(f"Extracted {len(transactions)} transactions:")
    for i, txn in enumerate(transactions):
        print(f"{i+1}. {txn}")

def test_date_parsing():
    """Test date parsing with various formats"""
    processor = PDFProcessor()
    
    test_dates = [
        '01/01/2024',
        '1-Jan-2024',
        '01-01-24',
        '2024-01-01',
        '01 Jan 2024',
        '1/1/24',
        'invalid_date',
        '32/13/2024',  # Invalid date
        ''
    ]
    
    print("Testing date parsing:")
    for date_str in test_dates:
        parsed = processor._parse_date(date_str)
        print(f"'{date_str}' -> {parsed}")

def test_amount_parsing():
    """Test amount parsing with various formats"""
    processor = PDFProcessor()
    
    test_amounts = [
        '5000.00',
        '5,000.00',
        '50,000.50',
        'Rs. 1000',
        '₹ 2000',
        '(500.00)',  # Negative
        '1,50,000.00',  # Indian format
        'invalid_amount',
        '',
        '-'
    ]
    
    print("\nTesting amount parsing:")
    for amount_str in test_amounts:
        parsed = processor._parse_amount(amount_str)
        print(f"'{amount_str}' -> {parsed}")

if __name__ == "__main__":
    print("🧪 Testing improved extraction logic\n")
    
    test_date_parsing()
    test_amount_parsing()
    print("\n" + "="*50 + "\n")
    test_sample_data()
