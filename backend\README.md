# PDF Processing Backend

This backend API provides advanced PDF processing capabilities for extracting transaction tables from bank statements, supporting both regular and scanned PDFs.

## Features

- **Table Extraction**: Uses Tabula-py and Camelot-py for extracting tables from regular PDFs
- **OCR Support**: Processes scanned/image-based PDFs using Tesseract OCR
- **Multi-Bank Support**: Supports major Indian banks (SBI, HDFC, ICICI, Axis, etc.)
- **Robust Error Handling**: Comprehensive error handling with user-friendly messages
- **CORS Enabled**: Ready for frontend integration
- **Data Validation**: Validates and structures extracted data into consistent JSON format

## Installation

### Prerequisites

1. **Python 3.8+**
2. **Java JRE 11+** (required for Tabula)
3. **Tesseract OCR** (for scanned PDF processing)

### Quick Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Run the setup script:
   ```bash
   python setup.py
   ```

### Manual Installation

If the setup script doesn't work, install dependencies manually:

#### System Dependencies

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install -y openjdk-11-jre-headless tesseract-ocr python3-opencv ghostscript
```

**macOS:**
```bash
brew install openjdk@11 tesseract ghostscript
```

**Windows:**
- Install Java JRE 11+ from Oracle or OpenJDK
- Install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki
- Add Tesseract to your PATH

#### Python Dependencies

```bash
pip install -r requirements.txt
```

## Usage

### Starting the Server

```bash
python app.py
```

The server will start on `http://localhost:5000`

### API Endpoints

#### 1. Health Check
```
GET /health
```

Response:
```json
{
  "status": "healthy",
  "message": "PDF processing API is running"
}
```

#### 2. Process PDF
```
POST /api/process-pdf
```

**Request:**
- Content-Type: `multipart/form-data`
- Body: PDF file with key `file`

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "txn_1234567890_hash",
        "date": "2024-01-15",
        "description": "ATM WITHDRAWAL",
        "debit": 5000.00,
        "credit": null,
        "balance": 25000.00
      }
    ],
    "bank_name": "HDFC",
    "extraction_method": "table_extraction",
    "total_transactions": 1
  },
  "message": "Successfully extracted 1 transactions"
}
```

**Response (Error):**
```json
{
  "error": "Processing failed",
  "message": "Unable to process the PDF file. Please ensure it's a valid bank statement.",
  "details": "Specific error details"
}
```

#### 3. Supported Banks
```
GET /api/supported-banks
```

Response:
```json
{
  "success": true,
  "banks": [
    {"id": "SBI", "name": "State Bank of India"},
    {"id": "HDFC", "name": "HDFC Bank"},
    ...
  ]
}
```

## Processing Methods

### 1. Regular PDFs
- **Primary**: Tabula-py for simple table structures
- **Fallback**: Camelot-py for complex table layouts
- **Data Format**: Pandas DataFrames converted to structured JSON

### 2. Scanned PDFs
- **OCR Engine**: Tesseract with image preprocessing
- **Image Enhancement**: Denoising, thresholding, and optimization
- **Text Parsing**: Pattern matching for transaction extraction

## Error Handling

The API provides comprehensive error handling for:
- Invalid file types
- File size limits (16MB max)
- Processing failures
- OCR errors
- Network issues

## Configuration

### Environment Variables

- `FLASK_ENV`: Set to `development` for debug mode
- `UPLOAD_FOLDER`: Directory for temporary file storage (default: `uploads`)
- `MAX_CONTENT_LENGTH`: Maximum file size in bytes (default: 16MB)

### Logging

Logs are written to console with INFO level by default. Adjust logging level in `app.py`:

```python
logging.basicConfig(level=logging.DEBUG)  # For verbose logging
```

## Troubleshooting

### Common Issues

1. **Java not found**: Ensure Java JRE 11+ is installed and in PATH
2. **Tesseract not found**: Install Tesseract and add to PATH
3. **Import errors**: Run `pip install -r requirements.txt`
4. **Permission errors**: Ensure write permissions for upload directory

### Testing

Test the API with curl:

```bash
curl -X POST -F "file=@sample_statement.pdf" http://localhost:5000/api/process-pdf
```

## Development

### Project Structure
```
backend/
├── app.py              # Flask application
├── pdf_processor.py    # Core PDF processing logic
├── requirements.txt    # Python dependencies
├── setup.py           # Installation script
└── README.md          # This file
```

### Adding New Banks

To add support for new banks, update the `bank_patterns` dictionary in `pdf_processor.py`:

```python
bank_patterns = {
    'NEW_BANK': ['NEW BANK NAME', 'NBK'],
    # ... existing patterns
}
```
