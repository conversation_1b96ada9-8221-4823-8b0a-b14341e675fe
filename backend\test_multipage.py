#!/usr/bin/env python3
"""
Test script for multi-page PDF extraction
"""

import sys
import os
import logging
from pdf_processor import PDFProcessor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_multipage_extraction(pdf_path: str):
    """
    Test multi-page PDF extraction
    """
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    print(f"🔍 Testing multi-page extraction for: {pdf_path}")
    print("=" * 60)
    
    try:
        # Initialize processor
        processor = PDFProcessor()
        print("✅ PDF Processor initialized")
        
        # Test bank detection
        text_content = processor._extract_text_for_detection(pdf_path)
        is_hdfc, confidence = processor.hdfc_processor.detect_hdfc_bank(text_content)
        print(f"🏦 HDFC Detection: {is_hdfc} (confidence: {confidence:.2f})")
        
        # Test if PDF is scanned
        is_scanned = processor._is_scanned_pdf(pdf_path)
        print(f"📄 Is scanned PDF: {is_scanned}")
        
        # Get page count
        try:
            import fitz
            doc = fitz.open(pdf_path)
            page_count = doc.page_count
            doc.close()
            print(f"📖 Total pages: {page_count}")
        except:
            print("📖 Could not determine page count")
        
        # Process the PDF
        print("\n🚀 Processing PDF...")
        result = processor.process_pdf(pdf_path)
        
        print(f"\n✅ Processing Results:")
        print(f"   - Total transactions: {len(result.get('transactions', []))}")
        print(f"   - Bank: {result.get('bank_name')}")
        print(f"   - Method: {result.get('extraction_method')}")
        print(f"   - Detection confidence: {result.get('detection_confidence', 0):.2f}")
        
        # Show sample transactions
        transactions = result.get('transactions', [])
        if transactions:
            print(f"\n📋 Sample transactions (showing first 5):")
            for i, txn in enumerate(transactions[:5]):
                print(f"   {i+1}. Date: {txn.get('date')}")
                print(f"      Narration: {txn.get('narration', txn.get('description', ''))[:60]}...")
                print(f"      Withdrawal: {txn.get('withdrawal_amt', txn.get('debit'))}")
                print(f"      Deposit: {txn.get('deposit_amt', txn.get('credit'))}")
                print(f"      Balance: {txn.get('closing_balance', txn.get('balance'))}")
                print()
            
            # Show date range
            dates = [txn.get('date') for txn in transactions if txn.get('date')]
            if dates:
                dates.sort()
                print(f"📅 Date range: {dates[0]} to {dates[-1]}")
        else:
            print("❌ No transactions extracted!")
        
        return result
        
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    Main function
    """
    if len(sys.argv) != 2:
        print("Usage: python test_multipage.py <pdf_path>")
        print("Example: python test_multipage.py uploads/sample_statement.pdf")
        return
    
    pdf_path = sys.argv[1]
    result = test_multipage_extraction(pdf_path)
    
    if result:
        print("\n🎉 Multi-page extraction test completed successfully!")
    else:
        print("\n💥 Multi-page extraction test failed!")

if __name__ == "__main__":
    main()
