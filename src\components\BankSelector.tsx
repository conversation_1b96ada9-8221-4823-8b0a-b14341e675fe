import React from 'react';
import { BankFormat } from '../types';
import { Check, CreditCard } from 'lucide-react';

interface BankSelectorProps {
  banks: BankFormat[];
  selectedBank: string | null;
  onBankSelect: (bankId: string) => void;
  detectedBank: string | null;
}

const BankSelector: React.FC<BankSelectorProps> = ({ 
  banks, 
  selectedBank, 
  onBankSelect,
  detectedBank 
}) => {
  return (
    <div className="w-full">
      {detectedBank && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg text-green-800 flex items-center">
          <Check className="w-5 h-5 mr-2 text-green-600" />
          <p>We detected this as a <span className="font-semibold">{detectedBank}</span> statement. Please confirm or select a different bank.</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {banks.map(bank => (
          <div
            key={bank.id}
            className={`card p-4 cursor-pointer transition-all duration-300 border-2 hover:shadow-md 
              ${selectedBank === bank.id ? 'border-blue-500 bg-blue-50' : 'border-transparent'}
            `}
            onClick={() => onBankSelect(bank.id)}
          >
            <div className="flex items-center">
              {bank.logo ? (
                <img src={bank.logo} alt={bank.name} className="w-12 h-12 object-contain mr-3" />
              ) : (
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                  <CreditCard className="w-6 h-6 text-gray-500" />
                </div>
              )}
              <div>
                <h3 className="font-medium text-gray-900">{bank.name}</h3>
                <p className="text-sm text-gray-500">
                  {selectedBank === bank.id && detectedBank === bank.id && "Detected & Selected"}
                  {selectedBank === bank.id && detectedBank !== bank.id && "Selected"}
                  {selectedBank !== bank.id && detectedBank === bank.id && "Detected"}
                  {selectedBank !== bank.id && detectedBank !== bank.id && "Compatible Format"}
                </p>
              </div>
              {selectedBank === bank.id && (
                <Check className="w-5 h-5 text-blue-600 ml-auto" />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BankSelector;