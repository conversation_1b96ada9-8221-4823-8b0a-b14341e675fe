@echo off
echo ========================================
echo Windows Installation Script
echo ========================================
echo.

echo Installing Python dependencies for Windows...
echo.

echo Step 1: Upgrading pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo Failed to upgrade pip
    pause
    exit /b 1
)

echo.
echo Step 2: Installing basic dependencies...
python -m pip install Flask Flask-CORS Werkzeug pandas numpy requests python-dateutil
if %errorlevel% neq 0 (
    echo Failed to install basic dependencies
    pause
    exit /b 1
)

echo.
echo Step 3: Installing PDF processing libraries...
python -m pip install tabula-py
if %errorlevel% neq 0 (
    echo Failed to install tabula-py
    pause
    exit /b 1
)

echo.
echo Step 4: Installing OCR dependencies...
python -m pip install pytesseract Pillow opencv-python-headless
if %errorlevel% neq 0 (
    echo Failed to install OCR dependencies
    pause
    exit /b 1
)

echo.
echo Step 5: Installing PDF libraries (trying multiple options)...

echo Trying PyMuPDF with pre-compiled wheel...
python -m pip install --only-binary=all PyMuPDF
if %errorlevel% neq 0 (
    echo PyMuPDF failed, trying alternative PDF libraries...
    python -m pip install pdfplumber PyPDF2
    if %errorlevel% neq 0 (
        echo Warning: Some PDF libraries failed to install
    )
)

echo.
echo Step 6: Installing Camelot (basic version)...
python -m pip install camelot-py[base]
if %errorlevel% neq 0 (
    echo Warning: Camelot installation failed, will use alternative methods
)

echo.
echo ========================================
echo Installation completed!
echo ========================================
echo.

echo Testing imports...
python -c "import flask, pandas, tabula; print('✓ Core libraries imported successfully')"
if %errorlevel% neq 0 (
    echo Some imports failed, but basic functionality should work
)

echo.
echo To start the backend server, run:
echo python app.py
echo.
pause
