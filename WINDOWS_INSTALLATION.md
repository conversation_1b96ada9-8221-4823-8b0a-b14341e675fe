# Windows Installation Guide

If you're encountering errors with the standard installation, follow this step-by-step guide for Windows.

## Problem
The error you're seeing is because PyMuPDF requires Visual Studio Build Tools to compile on Windows, which is not installed on your system.

## Solution Options

### Option 1: Quick Installation (Recommended)

1. **Open Command Prompt as Administrator**
   - Press `Win + X` and select "Command Prompt (Admin)" or "PowerShell (Admin)"

2. **Navigate to the backend directory**
   ```cmd
   cd "C:\Users\<USER>\Desktop\Data-Driven ERP Model\Application Model\PDF to Tally XML Project\V0\with python data extractor\copy\project\backend"
   ```

3. **Run the Windows installation script**
   ```cmd
   install-windows.bat
   ```

### Option 2: Manual Installation

If the batch script doesn't work, install packages one by one:

1. **Install basic dependencies**
   ```cmd
   pip install Flask Flask-CORS Werkzeug pandas numpy requests python-dateutil
   ```

2. **Install PDF processing**
   ```cmd
   pip install tabula-py
   ```

3. **Install OCR dependencies**
   ```cmd
   pip install pytesseract Pillow opencv-python-headless
   ```

4. **Try PyMuPDF with pre-compiled wheel**
   ```cmd
   pip install --only-binary=all PyMuPDF
   ```

5. **If PyMuPDF fails, install alternatives**
   ```cmd
   pip install pdfplumber PyPDF2
   ```

6. **Install Camelot (optional)**
   ```cmd
   pip install camelot-py[base]
   ```

### Option 3: Use Alternative Requirements File

Use the Windows-specific requirements file:

```cmd
pip install -r requirements-windows.txt
```

## Verification

After installation, test if everything works:

```cmd
python -c "import flask, pandas, tabula; print('✓ Installation successful')"
```

## Start the Application

1. **Start the backend**
   ```cmd
   python app.py
   ```

2. **In a new terminal, start the frontend**
   ```cmd
   cd ..
   npm run dev
   ```

## Troubleshooting

### If you still get compilation errors:

1. **Install Microsoft C++ Build Tools**
   - Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   - Install "C++ build tools" workload
   - Restart your computer

2. **Or use conda instead of pip**
   ```cmd
   conda install -c conda-forge flask pandas tabula-py pytesseract pillow opencv
   ```

### If Java is not found:

1. **Install Java JRE 11+**
   - Download from: https://www.oracle.com/java/technologies/downloads/
   - Or install OpenJDK: https://openjdk.org/

2. **Add Java to PATH**
   - Add Java bin directory to your system PATH
   - Verify with: `java -version`

### If Tesseract is not found:

1. **Install Tesseract OCR**
   - Download from: https://github.com/UB-Mannheim/tesseract/wiki
   - Install to default location

2. **Add Tesseract to PATH**
   - Add Tesseract installation directory to PATH
   - Usually: `C:\Program Files\Tesseract-OCR`
   - Verify with: `tesseract --version`

## What Each Library Does

- **Flask**: Web framework for the API
- **tabula-py**: Extracts tables from regular PDFs
- **PyMuPDF/pdfplumber**: PDF text extraction and processing
- **pytesseract**: OCR for scanned PDFs
- **opencv-python**: Image processing for better OCR
- **pandas**: Data manipulation and structuring
- **camelot-py**: Advanced table extraction (optional)

## Minimal Setup

If you want to start with just basic functionality:

```cmd
pip install Flask Flask-CORS pandas tabula-py
```

This will give you basic PDF table extraction without OCR capabilities.

## Testing

Once installed, you can test the setup:

```cmd
cd backend
python test-setup.py
```

This will check which libraries are available and what functionality is working.

## Need Help?

If you're still having issues:

1. Check that Python 3.8+ is installed: `python --version`
2. Check that pip is working: `pip --version`
3. Try installing packages one by one to identify which one is failing
4. Consider using a virtual environment:
   ```cmd
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements-windows.txt
   ```

The application is designed to work even if some libraries are missing - it will just have reduced functionality.
