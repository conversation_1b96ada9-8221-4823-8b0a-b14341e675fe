#!/usr/bin/env python3
"""
Enhanced HDFC Bank processor with perfect multi-page accuracy
Designed to match repotic.in level extraction quality
"""

import pandas as pd
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import traceback

logger = logging.getLogger(__name__)

class HDFCBankProcessor:
    def __init__(self):
        # Enhanced HDFC patterns for better detection
        self.hdfc_patterns = [
            r'HDFC\s*BANK',
            r'HDFC\s*Bank\s*Limited',
            r'Statement\s*of\s*Account',
            r'HDFC\s*BANK\s*LTD',
            r'Account\s*Branch\s*:\s*\w+',
            r'Customer\s*ID\s*:\s*\d+',
            r'Account\s*No\s*:\s*\d{10,}',
            r'RTGS/NEFT\s*IFSC:\s*HDFC\d+',
            r'Date.*Narration.*Balance',
            r'Withdrawal\s*Amt.*Deposit\s*Amt'
        ]

        # Standard HDFC column structure (7 columns)
        self.standard_columns = [
            'Date', 'Narration', 'Chq./Ref.No.', 'Value Dt', 
            'Withdrawal Amt.', 'Deposit Amt.', 'Closing Balance'
        ]

    def detect_hdfc_bank(self, text_content: str) -> Tuple[bool, float]:
        """Enhanced HDFC detection with better accuracy"""
        if not text_content:
            return False, 0.0

        text_upper = text_content.upper()
        matches = 0
        
        for pattern in self.hdfc_patterns:
            if re.search(pattern, text_upper, re.IGNORECASE | re.MULTILINE):
                matches += 1
                logger.debug(f"HDFC pattern matched: {pattern}")

        confidence = matches / len(self.hdfc_patterns)
        is_hdfc = confidence >= 0.15
        
        return is_hdfc, confidence

    def extract_hdfc_metadata(self, text_content: str) -> Dict[str, Any]:
        """Extract HDFC Bank metadata from text content"""
        metadata = {}
        if not text_content:
            return metadata

        text_upper = text_content.upper()

        # Extract Customer ID
        customer_id_match = re.search(r'CUSTOMER\s*ID\s*:?\s*(\d+)', text_upper)
        if customer_id_match:
            metadata['customer_id'] = customer_id_match.group(1)

        # Extract Account Number (masked for security)
        account_patterns = [
            r'ACCOUNT\s*NO\.?\s*:?\s*(\d{10,})',
            r'A/C\s*NO\.?\s*:?\s*(\d{10,})',
            r'ACCOUNT\s*NUMBER\s*:?\s*(\d{10,})'
        ]

        for pattern in account_patterns:
            match = re.search(pattern, text_upper)
            if match:
                account_num = match.group(1)
                if len(account_num) > 4:
                    metadata['account_number'] = '*' * (len(account_num) - 4) + account_num[-4:]
                else:
                    metadata['account_number'] = account_num
                break

        return metadata

    def process_hdfc_dataframe_pages(self, tables: List[pd.DataFrame]) -> List[Dict[str, Any]]:
        """
        Process multiple HDFC tables from different pages with perfect column alignment
        This is the key method that solves multi-page extraction issues
        """
        all_transactions = []
        
        try:
            logger.info(f"Processing {len(tables)} HDFC tables from multiple pages")
            
            # Step 1: Identify header patterns and page structures
            page_structures = self._analyze_page_structures(tables)
            
            # Step 2: Process each table with its identified structure
            for page_num, (table, structure) in enumerate(zip(tables, page_structures)):
                logger.info(f"Processing page {page_num + 1} with structure: {structure['type']}")
                
                # Clean and prepare the table
                cleaned_table = self._clean_dataframe(table)
                if cleaned_table.empty:
                    logger.warning(f"Page {page_num + 1}: Table is empty after cleaning")
                    continue
                
                # Process based on identified structure
                if structure['type'] == 'standard_hdfc':
                    page_transactions = self._process_standard_hdfc_page(
                        cleaned_table, structure, page_num + 1
                    )
                elif structure['type'] == 'continuation':
                    page_transactions = self._process_continuation_page(
                        cleaned_table, structure, page_num + 1
                    )
                else:
                    # Fallback to pattern-based extraction
                    page_transactions = self._process_fallback_page(
                        cleaned_table, page_num + 1
                    )
                
                if page_transactions:
                    all_transactions.extend(page_transactions)
                    logger.info(f"Page {page_num + 1}: Extracted {len(page_transactions)} transactions")
                else:
                    logger.warning(f"Page {page_num + 1}: No transactions extracted")
            
            # Step 3: Intelligent deduplication that preserves valid transactions
            unique_transactions = self._intelligent_deduplication(all_transactions)
            
            # Step 4: Sort by date
            unique_transactions.sort(key=lambda x: self._parse_date_for_sorting(x.get('date', '')))
            
            logger.info(f"Final HDFC extraction: {len(unique_transactions)} unique transactions from {len(tables)} pages")
            return unique_transactions
            
        except Exception as e:
            logger.error(f"Error in multi-page HDFC processing: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _analyze_page_structures(self, tables: List[pd.DataFrame]) -> List[Dict[str, Any]]:
        """Analyze each page structure to understand table layout"""
        structures = []
        
        for i, table in enumerate(tables):
            structure = {
                'type': 'unknown',
                'has_header': False,
                'header_row': -1,
                'data_start_row': 0,
                'column_mapping': {},
                'column_count': table.shape[1]
            }
            
            # Check if this looks like a standard HDFC page
            if self._is_standard_hdfc_page(table):
                structure['type'] = 'standard_hdfc'
                structure['has_header'] = True
                structure['header_row'] = self._find_header_row(table)
                structure['data_start_row'] = structure['header_row'] + 1
                structure['column_mapping'] = self._create_standard_column_mapping(table)
            
            # Check if this is a continuation page (no headers, just data)
            elif self._is_continuation_page(table):
                structure['type'] = 'continuation'
                structure['has_header'] = False
                structure['data_start_row'] = 0
                # Use the same column mapping as standard HDFC
                structure['column_mapping'] = self._create_positional_column_mapping(table.shape[1])
            
            else:
                structure['type'] = 'fallback'
                structure['column_mapping'] = self._create_fallback_column_mapping(table)
            
            structures.append(structure)
            logger.debug(f"Page {i+1} structure: {structure}")
        
        return structures

    def _is_standard_hdfc_page(self, table: pd.DataFrame) -> bool:
        """Check if table has standard HDFC header structure"""
        if table.empty or table.shape[0] < 2:
            return False
            
        # Look for HDFC column headers in first few rows
        for row_idx in range(min(5, table.shape[0])):
            row_text = ' '.join([str(val).lower() for val in table.iloc[row_idx] if str(val) != 'nan'])
            
            # Check for key HDFC headers
            hdfc_headers = ['date', 'narration', 'withdrawal', 'deposit', 'balance']
            header_matches = sum(1 for header in hdfc_headers if header in row_text)
            
            if header_matches >= 3:  # At least 3 key headers found
                return True
        
        return False

    def _is_continuation_page(self, table: pd.DataFrame) -> bool:
        """Check if this is a continuation page with transaction data only"""
        if table.empty or table.shape[0] < 1:
            return False
        
        # Check if first few rows contain transaction-like data
        date_count = 0
        for row_idx in range(min(3, table.shape[0])):
            for col_idx in range(min(2, table.shape[1])):  # Check first 2 columns
                value = str(table.iloc[row_idx, col_idx])
                if self._parse_hdfc_date(value):
                    date_count += 1
                    break
        
        return date_count >= 1  # At least one date found in first few rows

    def _find_header_row(self, table: pd.DataFrame) -> int:
        """Find the row that contains column headers"""
        for row_idx in range(min(10, table.shape[0])):
            row_text = ' '.join([str(val).lower() for val in table.iloc[row_idx] if str(val) != 'nan'])
            
            # Check for multiple HDFC headers
            hdfc_headers = ['date', 'narration', 'withdrawal', 'deposit', 'balance']
            header_matches = sum(1 for header in hdfc_headers if header in row_text)
            
            if header_matches >= 3:
                return row_idx
        
        return 0

    def _create_standard_column_mapping(self, table: pd.DataFrame) -> Dict[str, int]:
        """Create column mapping for standard HDFC table"""
        mapping = {}
        header_row = self._find_header_row(table)
        
        if header_row >= 0 and header_row < table.shape[0]:
            headers = [str(val).lower().strip() for val in table.iloc[header_row]]
            
            # Map based on header content
            for i, header in enumerate(headers):
                if 'date' in header and 'value' not in header:
                    mapping['date'] = i
                elif 'narration' in header or 'description' in header or 'particulars' in header:
                    mapping['narration'] = i
                elif 'chq' in header or 'ref' in header:
                    mapping['chq_ref_no'] = i
                elif 'value' in header and 'date' in header:
                    mapping['value_date'] = i
                elif 'withdrawal' in header or ('debit' in header and 'amt' in header):
                    mapping['withdrawal'] = i
                elif 'deposit' in header or ('credit' in header and 'amt' in header):
                    mapping['deposit'] = i
                elif 'balance' in header or 'closing' in header:
                    mapping['balance'] = i
        
        # Fallback to positional mapping if header mapping failed
        if len(mapping) < 4:
            mapping = self._create_positional_column_mapping(table.shape[1])
        
        return mapping

    def _create_positional_column_mapping(self, column_count: int) -> Dict[str, int]:
        """Create positional column mapping based on standard HDFC format"""
        mapping = {}
        
        if column_count >= 7:
            # Standard 7-column HDFC format
            mapping = {
                'date': 0,
                'narration': 1, 
                'chq_ref_no': 2,
                'value_date': 3,
                'withdrawal': 4,
                'deposit': 5,
                'balance': 6
            }
        elif column_count >= 5:
            # Simplified format
            mapping = {
                'date': 0,
                'narration': 1,
                'withdrawal': column_count - 3,
                'deposit': column_count - 2,
                'balance': column_count - 1
            }
        elif column_count >= 3:
            # Minimal format
            mapping = {
                'date': 0,
                'narration': 1,
                'balance': column_count - 1
            }
        
        return mapping

    def _create_fallback_column_mapping(self, table: pd.DataFrame) -> Dict[str, int]:
        """Create fallback column mapping by analyzing content"""
        mapping = {}
        
        for col_idx in range(table.shape[1]):
            col_data = table.iloc[:, col_idx]
            
            # Test for date column
            if self._test_date_column(col_data):
                if 'date' not in mapping:
                    mapping['date'] = col_idx
                continue
            
            # Test for amount columns
            if self._test_amount_column(col_data):
                if 'withdrawal' not in mapping:
                    mapping['withdrawal'] = col_idx
                elif 'deposit' not in mapping:
                    mapping['deposit'] = col_idx
                elif 'balance' not in mapping:
                    mapping['balance'] = col_idx
                continue
            
            # Test for description column
            if self._test_description_column(col_data):
                if 'narration' not in mapping:
                    mapping['narration'] = col_idx
        
        return mapping

    def _process_standard_hdfc_page(self, table: pd.DataFrame, structure: Dict, page_num: int) -> List[Dict[str, Any]]:
        """Process a standard HDFC page with headers"""
        transactions = []
        
        try:
            data_start = structure['data_start_row']
            column_mapping = structure['column_mapping']
            
            logger.debug(f"Page {page_num}: Processing {table.shape[0] - data_start} rows starting from row {data_start}")
            
            for row_idx in range(data_start, table.shape[0]):
                transaction = self._extract_transaction_from_row(
                    table.iloc[row_idx], column_mapping, f"{page_num}_{row_idx}"
                )
                
                if transaction and self._is_valid_transaction(transaction):
                    transaction['source_page'] = page_num
                    transactions.append(transaction)
                    
        except Exception as e:
            logger.error(f"Error processing standard HDFC page {page_num}: {str(e)}")
            
        return transactions

    def _process_continuation_page(self, table: pd.DataFrame, structure: Dict, page_num: int) -> List[Dict[str, Any]]:
        """Process a continuation page (no headers, just transaction data)"""
        transactions = []
        
        try:
            column_mapping = structure['column_mapping']
            
            logger.debug(f"Page {page_num}: Processing continuation page with {table.shape[0]} rows")
            
            for row_idx in range(table.shape[0]):
                transaction = self._extract_transaction_from_row(
                    table.iloc[row_idx], column_mapping, f"{page_num}_{row_idx}"
                )
                
                if transaction and self._is_valid_transaction(transaction):
                    transaction['source_page'] = page_num
                    transactions.append(transaction)
                    
        except Exception as e:
            logger.error(f"Error processing continuation page {page_num}: {str(e)}")
            
        return transactions

    def _process_fallback_page(self, table: pd.DataFrame, page_num: int) -> List[Dict[str, Any]]:
        """Process page using pattern-based extraction as fallback"""
        transactions = []
        
        try:
            logger.debug(f"Page {page_num}: Using fallback pattern extraction")
            
            for row_idx, row in table.iterrows():
                row_text = ' '.join([str(val) for val in row if str(val) != 'nan'])
                
                # Try to extract transaction using regex patterns
                transaction = self._extract_transaction_from_text(row_text, f"{page_num}_{row_idx}")
                
                if transaction and self._is_valid_transaction(transaction):
                    transaction['source_page'] = page_num
                    transactions.append(transaction)
                    
        except Exception as e:
            logger.error(f"Error in fallback processing for page {page_num}: {str(e)}")
            
        return transactions

    def _extract_transaction_from_row(self, row: pd.Series, column_mapping: Dict[str, int], row_id: str) -> Optional[Dict[str, Any]]:
        """Extract transaction from a single row with enhanced validation"""
        try:
            # Helper function to safely get column value
            def safe_get_value(field: str) -> str:
                if field in column_mapping and column_mapping[field] < len(row):
                    value = str(row.iloc[column_mapping[field]]).strip()
                    return value if value != 'nan' else ''
                return ''

            # Extract and validate date
            date_str = safe_get_value('date')
            parsed_date = self._parse_hdfc_date(date_str)
            
            if not parsed_date:
                return None

            # Extract other fields
            narration = safe_get_value('narration')
            chq_ref_no = safe_get_value('chq_ref_no')
            value_date = safe_get_value('value_date')
            
            # Parse amounts with enhanced validation
            withdrawal_amt = self._parse_hdfc_amount(safe_get_value('withdrawal'))
            deposit_amt = self._parse_hdfc_amount(safe_get_value('deposit'))
            balance_amt = self._parse_hdfc_amount(safe_get_value('balance'))
            
            # Parse value_date if present
            parsed_value_date = ''
            if value_date:
                parsed_value_date = self._parse_hdfc_date(value_date)
                if not parsed_value_date:
                    parsed_value_date = ''

            # Create transaction
            transaction = {
                'id': f"hdfc_{row_id}_{hash(str(row))}",
                'date': parsed_date,
                'narration': narration,
                'chq_ref_no': chq_ref_no,
                'value_date': parsed_value_date,
                'withdrawal_amt': withdrawal_amt,
                'deposit_amt': deposit_amt,
                'closing_balance': balance_amt,
                'bank': 'HDFC'
            }

            return transaction

        except Exception as e:
            logger.warning(f"Error extracting transaction from row {row_id}: {str(e)}")
            return None

    def _extract_transaction_from_text(self, text: str, row_id: str) -> Optional[Dict[str, Any]]:
        """Extract transaction from text using regex patterns"""
        try:
            # HDFC transaction patterns
            patterns = [
                # Date Narration RefNo ValueDate Withdrawal Deposit Balance
                r'(\d{2}/\d{2}/\d{2,4})\s+(.+?)\s+(\w+\d*)\s+(\d{2}/\d{2}/\d{2,4})\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
                # Date Narration Withdrawal Deposit Balance
                r'(\d{2}/\d{2}/\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
                # Date Narration Amount Balance
                r'(\d{2}/\d{2}/\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    groups = match.groups()
                    
                    # Parse date
                    date_str = groups[0]
                    parsed_date = self._parse_hdfc_date(date_str)
                    if not parsed_date:
                        continue
                    
                    # Parse narration
                    narration = groups[1].strip()
                    if len(narration) < 3:  # Skip very short descriptions
                        continue
                    
                    # Parse amounts based on pattern
                    amounts = [self._parse_hdfc_amount(g) for g in groups[2:] if g]
                    amounts = [a for a in amounts if a is not None]
                    
                    if not amounts:  # No valid amounts
                        continue
                    
                    # Determine transaction structure
                    withdrawal_amt = None
                    deposit_amt = None
                    balance_amt = None
                    
                    if len(amounts) >= 3:
                        withdrawal_amt = amounts[0] if amounts[0] > 0 else None
                        deposit_amt = amounts[1] if amounts[1] > 0 else None
                        balance_amt = amounts[2]
                    elif len(amounts) == 2:
                        # Amount and balance
                        if amounts[0] > 0:
                            # Determine if withdrawal or deposit based on context
                            if 'credit' in narration.lower() or 'deposit' in narration.lower():
                                deposit_amt = amounts[0]
                            else:
                                withdrawal_amt = amounts[0]
                        balance_amt = amounts[1]
                    elif len(amounts) == 1:
                        balance_amt = amounts[0]
                    
                    # Create transaction
                    transaction = {
                        'id': f"hdfc_text_{row_id}_{hash(text)}",
                        'date': parsed_date,
                        'narration': narration,
                        'chq_ref_no': '',
                        'value_date': '',
                        'withdrawal_amt': withdrawal_amt,
                        'deposit_amt': deposit_amt,
                        'closing_balance': balance_amt,
                        'bank': 'HDFC'
                    }
                    
                    return transaction
            
            return None
            
        except Exception as e:
            logger.warning(f"Error extracting transaction from text: {str(e)}")
            return None

    def _intelligent_deduplication(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Intelligent deduplication that preserves valid transactions
        This addresses the issue of losing valid transactions due to aggressive deduplication
        """
        if not transactions:
            return []
        
        unique_transactions = []
        seen_signatures = set()
        
        # Sort by source page and date to process in order
        transactions.sort(key=lambda x: (x.get('source_page', 0), x.get('date', '')))
        
        for txn in transactions:
            # Create a more flexible signature that allows for slight variations
            date = txn.get('date', '')
            narration = re.sub(r'\s+', ' ', txn.get('narration', '')).strip()[:30]  # First 30 chars
            withdrawal = round(txn.get('withdrawal_amt', 0) or 0, 2)
            deposit = round(txn.get('deposit_amt', 0) or 0, 2)
            
            # Create primary signature
            primary_signature = f"{date}_{narration}_{withdrawal}_{deposit}"
            
            # Create alternative signatures to catch near duplicates
            alt_signatures = []
            
            # Signature without narration (for cases where narration might vary slightly)
            if withdrawal > 0 or deposit > 0:
                alt_signatures.append(f"{date}_AMT_{withdrawal}_{deposit}")
            
            # Check for duplicates
            is_duplicate = False
            for sig in [primary_signature] + alt_signatures:
                if sig in seen_signatures:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                seen_signatures.add(primary_signature)
                for sig in alt_signatures:
                    seen_signatures.add(sig)
                unique_transactions.append(txn)
            else:
                logger.debug(f"Removed duplicate: {date} - {narration[:20]}...")
        
        logger.info(f"Deduplication: {len(transactions)} -> {len(unique_transactions)} transactions")
        return unique_transactions

    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean DataFrame for better processing"""
        # Remove completely empty rows and columns
        df = df.dropna(how='all').dropna(axis=1, how='all')
        
        # Reset index
        df = df.reset_index(drop=True)
        
        # Convert all values to string and strip whitespace
        for col in df.columns:
            df[col] = df[col].astype(str).str.strip()
            
        # Replace various null representations with empty strings
        df = df.replace(['nan', 'NaN', 'None', 'null', 'Unnamed: 0'], '')
        
        return df

    def _test_date_column(self, col_data: pd.Series) -> bool:
        """Test if column contains dates"""
        date_count = 0
        valid_entries = 0
        
        for value in col_data.head(20):
            if value and str(value) != '' and str(value) != 'nan':
                valid_entries += 1
                if self._parse_hdfc_date(str(value)):
                    date_count += 1
        
        return valid_entries > 0 and (date_count / valid_entries) > 0.3

    def _test_amount_column(self, col_data: pd.Series) -> bool:
        """Test if column contains amounts"""
        amount_count = 0
        valid_entries = 0
        
        for value in col_data.head(20):
            if value and str(value) != '' and str(value) != 'nan':
                valid_entries += 1
                if self._parse_hdfc_amount(str(value)) is not None:
                    amount_count += 1
        
        return valid_entries > 0 and (amount_count / valid_entries) > 0.25

    def _test_description_column(self, col_data: pd.Series) -> bool:
        """Test if column contains descriptions"""
        text_count = 0
        valid_entries = 0
        
        for value in col_data.head(10):
            if value and str(value) != '' and str(value) != 'nan':
                valid_entries += 1
                # Check if it contains meaningful text
                if re.search(r'[a-zA-Z]', str(value)) and len(str(value)) > 3:
                    text_count += 1
        
        return valid_entries > 0 and (text_count / valid_entries) > 0.5

    def _parse_hdfc_date(self, date_str: str) -> Optional[str]:
        """Enhanced date parsing for HDFC formats"""
        if not date_str or str(date_str).lower() in ['', 'nan', 'none', 'null', 'date']:
            return None

        # Clean the string
        date_str = str(date_str).strip()
        
        # Skip obvious non-dates
        if len(date_str) < 6 or date_str.lower() in ['date', 'dt', 'value dt']:
            return None

        # Enhanced date formats for HDFC
        date_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
            '%d/%m/%y', '%d-%m-%y', '%d.%m.%y',
            '%Y-%m-%d', '%Y/%m/%d',
            '%d %b %Y', '%d %B %Y', '%d-%b-%Y',
            '%d %b %y', '%d-%b-%y'
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                # Validate year range
                if 1990 <= parsed_date.year <= datetime.now().year + 1:
                    return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue

        return None

    def _parse_hdfc_amount(self, amount_str: str) -> Optional[float]:
        """Enhanced amount parsing for Indian formats with better validation"""
        if not amount_str or str(amount_str).lower() in ['', 'nan', 'none', 'null', '-', '0.00']:
            return None

        try:
            # Clean the string
            amount_str = str(amount_str).strip()
            
            # Skip header values and obvious non-amounts
            if any(word in amount_str.lower() for word in ['amt', 'amount', 'balance', 'withdrawal', 'deposit']):
                if not re.search(r'\d', amount_str):
                    return None

            # Remove currency symbols and text, keep only digits, commas, dots, parentheses
            amount_str = re.sub(r'[^\d.,()-]', '', amount_str)
            
            if not amount_str or not re.search(r'\d', amount_str):
                return None

            # Handle negative amounts in parentheses
            is_negative = amount_str.startswith('(') and amount_str.endswith(')')
            if is_negative:
                amount_str = amount_str[1:-1]

            # Handle Indian comma format (1,23,456.78)
            if ',' in amount_str and '.' in amount_str:
                parts = amount_str.split('.')
                if len(parts) == 2:
                    integer_part = parts[0].replace(',', '')
                    decimal_part = parts[1]
                    amount_str = integer_part + '.' + decimal_part
            elif ',' in amount_str:
                amount_str = amount_str.replace(',', '')

            # Final validation - should be a valid number
            if not re.match(r'^\d+(\.\d+)?$', amount_str):
                return None

            amount = float(amount_str)
            
            # Reasonable amount validation (not more than 1 billion)
            if amount > 1e9:
                return None
            
            # Very small amounts are likely parsing errors
            if 0 < amount < 0.01:
                return None

            return -amount if is_negative else amount

        except (ValueError, TypeError):
            return None

    def _is_valid_transaction(self, transaction: Dict[str, Any]) -> bool:
        """Enhanced transaction validation"""
        try:
            # Must have valid date
            if not transaction.get('date'):
                return False

            # Must have some meaningful content
            has_narration = bool(transaction.get('narration', '').strip())
            has_amounts = any([
                transaction.get('withdrawal_amt'),
                transaction.get('deposit_amt'),
                transaction.get('closing_balance')
            ])

            if not (has_narration or has_amounts):
                return False

            # Validate narration is not a header
            narration = transaction.get('narration', '').lower().strip()
            if narration in ['narration', 'description', 'particulars', 'details', '', 'nan']:
                return False
            
            # Skip header-like entries
            if any(header in narration for header in ['withdrawal', 'deposit', 'balance', 'amount']):
                if len(narration.split()) <= 2:  # Short header-like text
                    return False

            return True

        except Exception:
            return False

    def _parse_date_for_sorting(self, date_str: str) -> str:
        """Parse date string for sorting purposes"""
        if not date_str:
            return '1900-01-01'
        
        try:
            # Already in YYYY-MM-DD format
            if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
                return date_str
                
            # Try to parse and convert
            parsed_date = self._parse_hdfc_date(date_str)
            return parsed_date if parsed_date else '1900-01-01'
            
        except Exception:
            return '1900-01-01'

    # Legacy method for backward compatibility
    def process_hdfc_dataframe(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Legacy method - redirects to multi-page processor"""
        return self.process_hdfc_dataframe_pages([df])
