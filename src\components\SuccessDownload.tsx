import React from 'react';
import { CheckCircle, Download, RotateCcw } from 'lucide-react';

interface SuccessDownloadProps {
  onDownloadAgain: () => void;
  onReset: () => void;
  filename: string;
}

const SuccessDownload: React.FC<SuccessDownloadProps> = ({ 
  onDownloadAgain, 
  onReset,
  filename
}) => {
  return (
    <div className="w-full max-w-lg mx-auto">
      <div className="card p-8 text-center">
        <div className="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-6">
          <CheckCircle className="w-10 h-10 text-green-600" />
        </div>
        
        <h3 className="text-2xl font-semibold text-gray-900 mb-2">
          Conversion Successful!
        </h3>
        
        <p className="text-gray-600 mb-6">
          Your Tally XML file has been generated and downloaded successfully.
          You can now import this file into Tally.
        </p>
        
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <button 
            className="btn btn-primary flex items-center"
            onClick={onDownloadAgain}
          >
            <Download className="w-4 h-4 mr-2" />
            Download Again
          </button>
          
          <button 
            className="btn btn-secondary flex items-center"
            onClick={onReset}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Convert Another File
          </button>
        </div>
        
        <div className="mt-6 p-3 bg-blue-50 rounded-lg text-blue-800 text-sm">
          <p>File <span className="font-medium">{filename}</span> has been downloaded to your default downloads folder.</p>
        </div>
      </div>
      
      <div className="mt-8">
        <h4 className="text-lg font-medium text-gray-900 mb-3">
          Next Steps:
        </h4>
        
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Open your Tally ERP software</li>
          <li>Go to <span className="font-medium">Import → Data → XML</span></li>
          <li>Select the downloaded XML file</li>
          <li>Review and confirm the imported transactions</li>
          <li>Assign proper ledgers to the "Suspense Account" entries</li>
        </ol>
      </div>
    </div>
  );
};

export default SuccessDownload;