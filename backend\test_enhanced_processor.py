#!/usr/bin/env python3
"""
Test script for the enhanced HDFC PDF processor
"""

import sys
import os
import logging
from pdf_processor import PDFProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_enhanced_processor():
    """Test the enhanced HDFC processor"""
    print("Testing Enhanced HDFC PDF Processor")
    print("=" * 50)
    
    # Initialize processor
    try:
        processor = PDFProcessor()
        print("✅ PDFProcessor initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize PDFProcessor: {e}")
        return False
    
    # Test method availability
    methods_to_test = [
        '_process_hdfc_pdf_enhanced',
        '_tabula_lattice_strategy',
        '_tabula_stream_strategy', 
        '_tabula_guess_strategy',
        '_camelot_strategy',
        '_advanced_deduplication'
    ]
    
    for method_name in methods_to_test:
        if hasattr(processor, method_name):
            print(f"✅ Method {method_name} is available")
        else:
            print(f"❌ Method {method_name} is missing")
            return False
    
    # Test strategy methods with dummy data
    print("\nTesting strategy methods...")
    
    # Test advanced deduplication
    try:
        sample_transactions = [
            {
                'date': '2024-01-01',
                'narration': 'Test Transaction 1',
                'withdrawal_amt': 100.0,
                'deposit_amt': 0
            },
            {
                'date': '2024-01-01',
                'narration': 'Test Transaction 1',  # Duplicate
                'withdrawal_amt': 100.0,
                'deposit_amt': 0
            },
            {
                'date': '2024-01-02',
                'narration': 'Test Transaction 2',
                'withdrawal_amt': 0,
                'deposit_amt': 200.0
            }
        ]
        
        deduplicated = processor._advanced_deduplication(sample_transactions)
        if len(deduplicated) == 2:  # Should remove 1 duplicate
            print("✅ Advanced deduplication working correctly")
        else:
            print(f"❌ Advanced deduplication failed - expected 2, got {len(deduplicated)}")
            
    except Exception as e:
        print(f"❌ Advanced deduplication test failed: {e}")
        return False
    
    print("\n🎉 All enhanced processor tests passed!")
    return True

def test_imports():
    """Test required imports"""
    print("Testing imports...")
    
    try:
        import pandas as pd
        print("✅ pandas imported")
    except ImportError:
        print("❌ pandas not available")
        return False
    
    try:
        import tabula
        print("✅ tabula imported")
    except ImportError:
        print("❌ tabula not available")
        return False
    
    try:
        import camelot
        print("✅ camelot imported")
    except ImportError:
        print("⚠️  camelot not available (optional)")
    
    return True

if __name__ == "__main__":
    print("Enhanced HDFC PDF Processor Test Suite")
    print("=" * 60)
    
    # Test imports first
    if not test_imports():
        print("❌ Import tests failed")
        sys.exit(1)
    
    print()
    
    # Test enhanced processor
    if not test_enhanced_processor():
        print("❌ Enhanced processor tests failed")
        sys.exit(1)
    
    print("\n🎉 All tests completed successfully!")
    print("The enhanced HDFC PDF processor is ready to use.")
