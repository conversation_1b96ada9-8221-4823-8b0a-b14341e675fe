@echo off
echo ========================================
echo Bank Statement to Tally XML Converter
echo ========================================
echo.

echo Starting Backend API Server...
echo.
start "Backend API" cmd /k "cd backend && python app.py"

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend Development Server...
echo.
start "Frontend Dev Server" cmd /k "npm run dev"

echo.
echo ========================================
echo Both servers are starting...
echo.
echo Backend API: http://localhost:5000
echo Frontend: http://localhost:5173
echo.
echo Press any key to close this window...
echo ========================================
pause > nul
