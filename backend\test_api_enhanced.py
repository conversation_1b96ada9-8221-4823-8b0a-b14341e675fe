#!/usr/bin/env python3
"""
Test the Flask API with enhanced HDFC processor
"""

import sys
import os
import logging
import tempfile
import requests
import json
from io import BytesIO

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_api_health():
    """Test API health endpoint"""
    try:
        response = requests.get('http://localhost:5000/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'healthy':
                print("✅ API health check passed")
                return True
            else:
                print(f"❌ API unhealthy: {data}")
                return False
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API not accessible: {e}")
        return False

def test_supported_banks():
    """Test supported banks endpoint"""
    try:
        response = requests.get('http://localhost:5000/api/supported-banks', timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'banks' in data:
                banks = data['banks']
                hdfc_found = any(bank['id'] == 'HDFC' for bank in banks)
                if hdfc_found:
                    print(f"✅ Supported banks endpoint working - {len(banks)} banks including HDFC")
                    return True
                else:
                    print("❌ HDFC not found in supported banks")
                    return False
            else:
                print(f"❌ Invalid response format: {data}")
                return False
        else:
            print(f"❌ Supported banks endpoint failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Supported banks endpoint error: {e}")
        return False

def create_sample_pdf():
    """Create a sample PDF for testing (placeholder)"""
    # This would normally create a real PDF, but for testing we'll just create a dummy file
    # In a real scenario, you would use a library like reportlab to create a proper PDF
    sample_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
    return sample_content

def test_pdf_processing_endpoint():
    """Test PDF processing endpoint structure"""
    print("\nTesting PDF processing endpoint structure...")
    
    # Create a dummy file for testing
    sample_pdf = create_sample_pdf()
    
    try:
        files = {'file': ('test.pdf', BytesIO(sample_pdf), 'application/pdf')}
        response = requests.post(
            'http://localhost:5000/api/process-pdf',
            files=files,
            timeout=30
        )
        
        # We expect this to fail since it's not a real HDFC PDF
        # But we want to test the API structure
        if response.status_code in [400, 500]:  # Expected failure
            data = response.json()
            if 'error' in data or 'message' in data:
                print("✅ PDF processing endpoint structure working (expected failure with dummy PDF)")
                return True
            else:
                print(f"❌ Unexpected response format: {data}")
                return False
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ PDF processing endpoint error: {e}")
        return False

def test_enhanced_processor_integration():
    """Test that enhanced processor is integrated"""
    print("\nTesting enhanced processor integration...")
    
    # Import and test the processor directly
    try:
        from pdf_processor import PDFProcessor
        processor = PDFProcessor()
        
        # Check if enhanced method exists
        if hasattr(processor, '_process_hdfc_pdf_enhanced'):
            print("✅ Enhanced HDFC processor method available")
        else:
            print("❌ Enhanced HDFC processor method missing")
            return False
        
        # Check if strategy methods exist
        strategy_methods = [
            '_tabula_lattice_strategy',
            '_tabula_stream_strategy',
            '_tabula_guess_strategy',
            '_camelot_strategy',
            '_advanced_deduplication'
        ]
        
        for method in strategy_methods:
            if hasattr(processor, method):
                print(f"✅ {method} available")
            else:
                print(f"❌ {method} missing")
                return False
        
        # Check HDFC processor metadata method
        if hasattr(processor.hdfc_processor, 'extract_hdfc_metadata'):
            print("✅ HDFC metadata extraction available")
        else:
            print("❌ HDFC metadata extraction missing")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Cannot import processor: {e}")
        return False
    except Exception as e:
        print(f"❌ Processor integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("Enhanced HDFC PDF Processor API Test Suite")
    print("=" * 60)
    
    tests = [
        ("API Health Check", test_api_health),
        ("Supported Banks Endpoint", test_supported_banks),
        ("PDF Processing Endpoint Structure", test_pdf_processing_endpoint),
        ("Enhanced Processor Integration", test_enhanced_processor_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All API tests completed successfully!")
        print("The enhanced HDFC PDF processor is fully integrated with the Flask API.")
        return True
    else:
        print("❌ Some tests failed. Please check the API server and dependencies.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
