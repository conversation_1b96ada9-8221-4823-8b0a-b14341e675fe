#!/usr/bin/env python3
"""
Setup script for PDF processing backend
This script installs all required dependencies and sets up the environment
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def install_system_dependencies():
    """Install system-level dependencies"""
    system = platform.system().lower()
    
    if system == "linux":
        print("Installing system dependencies for Linux...")
        commands = [
            "sudo apt-get update",
            "sudo apt-get install -y openjdk-11-jre-headless",
            "sudo apt-get install -y tesseract-ocr",
            "sudo apt-get install -y python3-opencv",
            "sudo apt-get install -y ghostscript"
        ]
        
        for cmd in commands:
            if not run_command(cmd, f"Running: {cmd}"):
                print(f"Warning: Failed to run {cmd}")
                
    elif system == "darwin":  # macOS
        print("Installing system dependencies for macOS...")
        commands = [
            "brew install openjdk@11",
            "brew install tesseract",
            "brew install ghostscript"
        ]
        
        for cmd in commands:
            if not run_command(cmd, f"Running: {cmd}"):
                print(f"Warning: Failed to run {cmd}")
                
    elif system == "windows":
        print("For Windows, please manually install:")
        print("1. Java JRE 11 or higher")
        print("2. Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("3. Add Tesseract to your PATH")
        
        # Try to detect if tesseract is available
        try:
            subprocess.run(["tesseract", "--version"], check=True, capture_output=True)
            print("✓ Tesseract found in PATH")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("✗ Tesseract not found in PATH")
            print("Please install Tesseract and add it to your PATH")

def install_python_dependencies():
    """Install Python dependencies"""
    print("\nInstalling Python dependencies...")
    
    # Upgrade pip first
    run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip")
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        return run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing Python packages")
    else:
        print("requirements.txt not found!")
        return False

def verify_installation():
    """Verify that all dependencies are properly installed"""
    print("\nVerifying installation...")
    
    try:
        # Test imports
        import flask
        import pandas
        import tabula
        import camelot
        import pytesseract
        import cv2
        import fitz
        print("✓ All Python packages imported successfully")
        
        # Test Java (required for tabula)
        subprocess.run(["java", "-version"], check=True, capture_output=True)
        print("✓ Java is available")
        
        # Test Tesseract
        subprocess.run(["tesseract", "--version"], check=True, capture_output=True)
        print("✓ Tesseract is available")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except subprocess.CalledProcessError:
        print("✗ System dependency check failed")
        return False
    except FileNotFoundError as e:
        print(f"✗ System dependency not found: {e}")
        return False

def main():
    """Main setup function"""
    print("PDF Processing Backend Setup")
    print("=" * 40)
    
    # Install system dependencies
    install_system_dependencies()
    
    # Install Python dependencies
    if not install_python_dependencies():
        print("\n✗ Failed to install Python dependencies")
        sys.exit(1)
    
    # Verify installation
    if verify_installation():
        print("\n✓ Setup completed successfully!")
        print("\nTo start the backend server, run:")
        print("python app.py")
    else:
        print("\n✗ Setup verification failed")
        print("Please check the error messages above and resolve any issues")
        sys.exit(1)

if __name__ == "__main__":
    main()
