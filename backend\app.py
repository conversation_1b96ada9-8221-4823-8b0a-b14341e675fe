from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import logging
from werkzeug.utils import secure_filename
import traceback
import tempfile

# Try to import PDF processor with error handling
try:
    from pdf_processor import PDFProcessor
    PDF_PROCESSOR_AVAILABLE = True
except ImportError as e:
    PDF_PROCESSOR_AVAILABLE = False
    PDF_PROCESSOR_ERROR = str(e)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create upload directory if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'PDF processing API is running'})

@app.route('/api/process-pdf', methods=['POST'])
def process_pdf():
    """
    Main endpoint to process PDF files and extract transaction tables
    """
    # Check if PDF processor is available
    if not PDF_PROCESSOR_AVAILABLE:
        return jsonify({
            'error': 'PDF processing not available',
            'message': 'PDF processing libraries are not properly installed. Please run the installation script.',
            'details': f'Import error: {PDF_PROCESSOR_ERROR}'
        }), 500

    try:
        # Check if file is present in request
        if 'file' not in request.files:
            return jsonify({
                'error': 'No file provided',
                'message': 'Please upload a PDF file'
            }), 400

        file = request.files['file']

        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'error': 'No file selected',
                'message': 'Please select a PDF file to upload'
            }), 400

        # Check file extension
        if not allowed_file(file.filename):
            return jsonify({
                'error': 'Invalid file type',
                'message': 'Only PDF files are allowed'
            }), 400

        # Use temporary file to avoid conflicts
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_filepath = temp_file.name

        try:
            # Initialize PDF processor
            processor = PDFProcessor()

            # Process the PDF
            result = processor.process_pdf(temp_filepath)

            # Clean up temporary file
            os.unlink(temp_filepath)

            return jsonify({
                'success': True,
                'data': result,
                'message': f'Successfully extracted {len(result.get("transactions", []))} transactions',
                'extraction_details': {
                    'total_transactions': len(result.get("transactions", [])),
                    'extraction_method': result.get('extraction_method', 'unknown'),
                    'bank_name': result.get('bank_name', 'unknown'),
                    'detection_confidence': result.get('detection_confidence', 0),
                    'bank_metadata': result.get('bank_metadata', {})
                }
            })

        except Exception as processing_error:
            # Clean up temporary file in case of error
            if os.path.exists(temp_filepath):
                os.unlink(temp_filepath)
            raise processing_error

    except Exception as e:
        logger.error(f"Error processing PDF: {str(e)}")
        logger.error(traceback.format_exc())

        return jsonify({
            'error': 'Processing failed',
            'message': 'Unable to process the PDF file. Please ensure it\'s a valid bank statement.',
            'details': str(e)
        }), 500

@app.route('/api/supported-banks', methods=['GET'])
def get_supported_banks():
    """Get list of supported bank formats"""
    banks = [
        {'id': 'SBI', 'name': 'State Bank of India'},
        {'id': 'HDFC', 'name': 'HDFC Bank'},
        {'id': 'ICICI', 'name': 'ICICI Bank'},
        {'id': 'AXIS', 'name': 'Axis Bank'},
        {'id': 'KOTAK', 'name': 'Kotak Mahindra Bank'},
        {'id': 'PNB', 'name': 'Punjab National Bank'},
        {'id': 'BOB', 'name': 'Bank of Baroda'},
        {'id': 'CANARA', 'name': 'Canara Bank'},
        {'id': 'UNION', 'name': 'Union Bank of India'},
        {'id': 'GENERIC', 'name': 'Other Banks (Generic Format)'}
    ]
    
    return jsonify({
        'success': True,
        'banks': banks
    })

@app.errorhandler(413)
def too_large(error):
    """Handle file too large error"""
    return jsonify({
        'error': 'File too large',
        'message': 'File size exceeds the maximum limit of 16MB'
    }), 413

@app.errorhandler(500)
def internal_error(error):
    """Handle internal server errors"""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred. Please try again later.'
    }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
