/**
 * Test file for enhanced TransactionPreview component
 * This file demonstrates the enhanced validation features
 */

import React from 'react';
import { Transaction } from '../types';

// Sample test data for the enhanced TransactionPreview
export const sampleTransactionsWithValidation: Transaction[] = [
  // Valid transaction
  {
    id: '1',
    date: '01/01/24',
    description: 'ATM Withdrawal at Mumbai Main Branch',
    refNo: 'ATM123456',
    valueDate: '01/01/24',
    debit: 1000.00,
    credit: null,
    balance: 9000.00
  },
  // Transaction with missing date (invalid)
  {
    id: '2',
    date: '',
    description: 'Salary Credit from Company XYZ',
    refNo: 'SAL789012',
    valueDate: '02/01/24',
    debit: null,
    credit: 50000.00,
    balance: 59000.00
  },
  // Transaction with missing amount (invalid)
  {
    id: '3',
    date: '03/01/24',
    description: 'Online Transfer to <PERSON>',
    refNo: 'TXN345678',
    valueDate: '03/01/24',
    debit: null,
    credit: null,
    balance: 59000.00
  },
  // Transaction with missing description (invalid)
  {
    id: '4',
    date: '04/01/24',
    description: '',
    refNo: 'TXN456789',
    valueDate: '04/01/24',
    debit: 500.00,
    credit: null,
    balance: 58500.00
  },
  // Valid transaction
  {
    id: '5',
    date: '05/01/24',
    description: 'Interest Credit for January 2024',
    refNo: 'INT567890',
    valueDate: '05/01/24',
    debit: null,
    credit: 150.00,
    balance: 58650.00
  },
  // Duplicate transaction (should be flagged)
  {
    id: '6',
    date: '01/01/24',
    description: 'ATM Withdrawal at Mumbai Main Branch',
    refNo: 'ATM123456',
    valueDate: '01/01/24',
    debit: 1000.00,
    credit: null,
    balance: 9000.00
  }
];

// Sample bank metadata for testing
export const sampleBankMetadata = {
  customer_id: '********',
  account_number: '************3456',
  ifsc_code: 'HDFC0001234',
  branch: 'MUMBAI MAIN BRANCH',
  statement_from: '01/01/2024',
  statement_to: '31/01/2024'
};

// Expected validation results for the sample data
export const expectedValidationResults = {
  totalTransactions: 6,
  validTransactions: 2,
  invalidTransactions: 4,
  dataIntegrityScore: 33.3, // 2 valid out of 6 total
  issues: {
    dateIssues: 1,
    amountIssues: 1,
    duplicateTransactions: 1,
    descriptionIssues: 1
  }
};

// Test component props
export const testProps = {
  transactions: sampleTransactionsWithValidation,
  onGenerateXML: () => console.log('Generate XML clicked'),
  bankName: 'HDFC Bank',
  onTransactionsChange: (updatedTransactions: Transaction[]) => {
    console.log('Transactions updated:', updatedTransactions);
  },
  detectionConfidence: 0.85,
  bankMetadata: sampleBankMetadata
};

/**
 * Manual test instructions:
 * 
 * 1. Import this test data in your App.tsx or a test component
 * 2. Pass the testProps to TransactionPreview component
 * 3. Verify the following features:
 * 
 * Validation Status Component:
 * - Should show 6 total transactions
 * - Should show 33.3% data integrity score (red/yellow indicator)
 * - Should show 2 valid transactions
 * - Should show 4 issues found
 * - Should display detailed issues: 1 missing date, 1 missing amount, 1 duplicate
 * 
 * Individual Transaction Validation:
 * - Transaction 1: Green checkmark (valid)
 * - Transaction 2: Yellow warning (missing date)
 * - Transaction 3: Yellow warning (missing amount)
 * - Transaction 4: Yellow warning (missing description)
 * - Transaction 5: Green checkmark (valid)
 * - Transaction 6: Yellow warning (duplicate)
 * 
 * Visual Indicators:
 * - Invalid transactions should have yellow left border
 * - Validation icons should appear in the date column
 * - Hover over warning icons should show issue details
 * 
 * Bank Metadata Display:
 * - Should show account number (masked)
 * - Should show customer ID
 * - Should show branch name
 * - Should show statement period
 */

// Usage example:
/*
import { testProps } from './test/TransactionPreviewEnhanced.test';

function TestPage() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Enhanced TransactionPreview Test</h1>
      <TransactionPreview {...testProps} />
    </div>
  );
}
*/
