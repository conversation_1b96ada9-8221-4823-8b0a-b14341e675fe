#!/usr/bin/env python3
"""
Integration test for the enhanced HDFC PDF processor
"""

import sys
import os
import logging
import tempfile
from pdf_processor import PDFProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_process_pdf_integration():
    """Test the main process_pdf method with enhanced processing"""
    print("Testing process_pdf integration with enhanced HDFC processing")
    print("=" * 70)
    
    # Initialize processor
    try:
        processor = PDFProcessor()
        print("✅ PDFProcessor initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize PDFProcessor: {e}")
        return False
    
    # Test HDFC detection
    print("\nTesting HDFC detection...")
    sample_hdfc_text = """
    HDFC BANK LIMITED
    Statement of Account
    Customer ID: ********
    Account No: ****************
    IFSC Code: HDFC0001234
    Branch: Mumbai Main Branch
    Statement Period: 01/01/2024 to 31/01/2024
    
    Date        Narration                   Chq./Ref.No.    Value Dt    Withdrawal Amt.    Deposit Amt.    Closing Balance
    01/01/2024  Opening Balance                                                                              10,000.00
    02/01/2024  ATM Withdrawal              ATM123456        02/01/2024      500.00                         9,500.00
    03/01/2024  Salary Credit               SAL789012        03/01/2024                      50,000.00      59,500.00
    """
    
    is_hdfc, confidence = processor.hdfc_processor.detect_hdfc_bank(sample_hdfc_text)
    if is_hdfc and confidence > 0.15:
        print(f"✅ HDFC detection working - confidence: {confidence:.2f}")
    else:
        print(f"❌ HDFC detection failed - confidence: {confidence:.2f}")
        return False
    
    # Test metadata extraction
    print("\nTesting metadata extraction...")
    metadata = processor.hdfc_processor.extract_hdfc_metadata(sample_hdfc_text)

    expected_fields = ['customer_id', 'account_number', 'ifsc_code', 'branch']
    for field in expected_fields:
        if field in metadata and metadata[field]:
            print(f"✅ {field}: {metadata[field]}")
        else:
            print(f"⚠️  {field}: Not extracted")

    # Check if at least some metadata was extracted
    if len(metadata) > 0:
        print(f"✅ Metadata extraction working - extracted {len(metadata)} fields")
    else:
        print("❌ No metadata extracted")
        return False
    
    # Test enhanced processing methods
    print("\nTesting enhanced processing methods...")
    
    # Test strategy method signatures
    strategy_methods = [
        '_tabula_lattice_strategy',
        '_tabula_stream_strategy',
        '_tabula_guess_strategy',
        '_camelot_strategy'
    ]
    
    for method_name in strategy_methods:
        method = getattr(processor, method_name)
        if callable(method):
            print(f"✅ {method_name} is callable")
        else:
            print(f"❌ {method_name} is not callable")
            return False
    
    # Test advanced deduplication with more complex data
    print("\nTesting advanced deduplication with complex data...")
    
    complex_transactions = [
        {
            'date': '2024-01-01',
            'narration': 'ATM WITHDRAWAL AT MUMBAI MAIN BRANCH',
            'withdrawal_amt': 1000.0,
            'deposit_amt': 0,
            'closing_balance': 9000.0
        },
        {
            'date': '2024-01-01',
            'narration': 'ATM WITHDRAWAL AT MUMBAI MAIN BRANCH',  # Exact duplicate
            'withdrawal_amt': 1000.0,
            'deposit_amt': 0,
            'closing_balance': 9000.0
        },
        {
            'date': '2024-01-01',
            'narration': 'ATM  WITHDRAWAL  AT  MUMBAI  MAIN  BRANCH',  # Similar with extra spaces
            'withdrawal_amt': 1000.0,
            'deposit_amt': 0,
            'closing_balance': 9000.0
        },
        {
            'date': '2024-01-02',
            'narration': 'SALARY CREDIT FROM COMPANY XYZ',
            'withdrawal_amt': 0,
            'deposit_amt': 50000.0,
            'closing_balance': 59000.0
        }
    ]
    
    deduplicated = processor._advanced_deduplication(complex_transactions)
    
    if len(deduplicated) == 2:  # Should keep only 2 unique transactions
        print(f"✅ Advanced deduplication working - reduced {len(complex_transactions)} to {len(deduplicated)} transactions")
        for i, txn in enumerate(deduplicated):
            print(f"   Transaction {i+1}: {txn['date']} - {txn['narration'][:30]}...")
    else:
        print(f"❌ Advanced deduplication failed - expected 2, got {len(deduplicated)}")
        return False
    
    print("\n🎉 Integration tests completed successfully!")
    return True

def test_error_handling():
    """Test error handling in enhanced processor"""
    print("\nTesting error handling...")
    
    processor = PDFProcessor()
    
    # Test with empty file path
    try:
        result = processor._process_hdfc_pdf_enhanced("")
        print("❌ Should have failed with empty file path")
        return False
    except Exception as e:
        print(f"✅ Correctly handled empty file path: {type(e).__name__}")
    
    # Test with non-existent file
    try:
        result = processor._process_hdfc_pdf_enhanced("non_existent_file.pdf")
        print("❌ Should have failed with non-existent file")
        return False
    except Exception as e:
        print(f"✅ Correctly handled non-existent file: {type(e).__name__}")
    
    return True

if __name__ == "__main__":
    print("Enhanced HDFC PDF Processor Integration Test Suite")
    print("=" * 80)
    
    # Run integration tests
    if not test_process_pdf_integration():
        print("❌ Integration tests failed")
        sys.exit(1)
    
    # Run error handling tests
    if not test_error_handling():
        print("❌ Error handling tests failed")
        sys.exit(1)
    
    print("\n🎉 All integration tests completed successfully!")
    print("The enhanced HDFC PDF processor is fully integrated and ready for production use.")
