import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileType, File as FileIcon, FilePlus, FileX } from 'lucide-react';
import { ProcessingStatus } from '../types';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  status: ProcessingStatus;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileSelect, status, error }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setSelectedFile(file);
      onFileSelect(file);
    }
  }, [onFileSelect]);
  
  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    multiple: false,
  });
  
  const isProcessing = status === ProcessingStatus.PROCESSING;
  const isError = status === ProcessingStatus.ERROR;
  
  return (
    <div className="w-full">
      <div 
        {...getRootProps()} 
        className={`card p-8 border-2 border-dashed transition-all duration-300 cursor-pointer
          ${isDragActive ? 'bg-blue-50 border-blue-300' : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50'}
          ${isDragReject ? 'border-red-300 bg-red-50' : ''}
          ${isError ? 'border-red-300' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          {selectedFile ? (
            <>
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                <FileIcon className="w-8 h-8" />
              </div>
              <div>
                <p className="text-lg font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
              {isProcessing && (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                  <p className="text-blue-600">Processing your PDF...</p>
                </div>
              )}
              {isError && (
                <div className="text-red-500 flex items-center space-x-2">
                  <FileX className="w-5 h-5" />
                  <span>{error || "Error processing file. Please try again."}</span>
                </div>
              )}
            </>
          ) : (
            <>
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 animate-pulse">
                <Upload className="w-8 h-8" />
              </div>
              <div>
                <p className="text-lg font-medium text-gray-900">
                  Drop your bank statement PDF here
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Supports statements from major banks like SBI, HDFC, ICICI
                </p>
              </div>
              <button 
                type="button" 
                className="btn btn-primary mt-4 flex items-center"
              >
                <FilePlus className="w-4 h-4 mr-2" />
                Select PDF File
              </button>
            </>
          )}
        </div>
      </div>
      
      {selectedFile && (
        <div className="mt-4 flex justify-end">
          <button 
            type="button" 
            className="btn btn-secondary flex items-center text-sm"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedFile(null);
            }}
            disabled={isProcessing}
          >
            <FileX className="w-4 h-4 mr-2" />
            Change File
          </button>
        </div>
      )}
    </div>
  );
};

export default FileUpload;