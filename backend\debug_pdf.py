#!/usr/bin/env python3
"""
Debug script to test PDF processing with detailed logging
"""

import logging
import sys
import os
from pdf_processor import PDFProcessor

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def debug_pdf_processing(pdf_path):
    """Debug PDF processing with detailed output"""
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    print(f"🔍 Debugging PDF: {pdf_path}")
    print(f"📁 File size: {os.path.getsize(pdf_path)} bytes")
    
    try:
        # Initialize processor
        processor = PDFProcessor()
        print("✅ PDF Processor initialized")
        
        # Test bank detection first
        bank_name = processor._detect_bank_from_pdf(pdf_path)
        print(f"🏦 Detected bank: {bank_name}")
        
        # Test if PDF is scanned
        is_scanned = processor._is_scanned_pdf(pdf_path)
        print(f"📄 Is scanned PDF: {is_scanned}")
        
        # Try Tabula extraction
        print("\n🔧 Testing Tabula extraction...")
        tabula_result = processor._extract_with_tabula(pdf_path)
        print(f"📊 Tabula transactions: {len(tabula_result.get('transactions', []))}")
        
        if tabula_result.get('transactions'):
            print("✅ Tabula extraction successful!")
            for i, txn in enumerate(tabula_result['transactions'][:3]):  # Show first 3
                print(f"   Transaction {i+1}: {txn}")
        else:
            print("⚠️ Tabula extraction returned no transactions")
        
        # Try Camelot extraction if available
        print("\n🔧 Testing Camelot extraction...")
        camelot_result = processor._extract_with_camelot(pdf_path)
        print(f"📊 Camelot transactions: {len(camelot_result.get('transactions', []))}")
        
        # Full processing test
        print("\n🚀 Testing full processing...")
        result = processor.process_pdf(pdf_path)
        print(f"✅ Full processing result:")
        print(f"   - Transactions: {len(result.get('transactions', []))}")
        print(f"   - Bank: {result.get('bank_name')}")
        print(f"   - Method: {result.get('extraction_method', 'table_extraction')}")
        
        if result.get('transactions'):
            print("\n📋 Sample transactions:")
            for i, txn in enumerate(result['transactions'][:3]):
                print(f"   {i+1}. Date: {txn.get('date')}, Desc: {txn.get('description', '')[:50]}...")
        
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")
        import traceback
        print(f"📋 Full traceback:\n{traceback.format_exc()}")

def main():
    """Main function"""
    
    # Check if PDF path is provided
    if len(sys.argv) < 2:
        print("Usage: python debug_pdf.py <path_to_pdf>")
        print("\nExample:")
        print("python debug_pdf.py uploads/statement.pdf")
        return
    
    pdf_path = sys.argv[1]
    debug_pdf_processing(pdf_path)

if __name__ == "__main__":
    main()
