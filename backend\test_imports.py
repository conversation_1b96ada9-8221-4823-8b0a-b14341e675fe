#!/usr/bin/env python3
"""
Test script to verify all imports are working
"""

print("Testing imports...")

try:
    import flask
    print("✅ Flask imported")
except ImportError as e:
    print(f"❌ Flask failed: {e}")

try:
    import pandas
    print("✅ Pandas imported")
except ImportError as e:
    print(f"❌ Pandas failed: {e}")

try:
    import tabula
    print("✅ Tabula imported")
except ImportError as e:
    print(f"❌ Tabula failed: {e}")

try:
    import camelot
    print("✅ Camelot imported")
except ImportError as e:
    print(f"❌ Camelot failed: {e}")

try:
    import pytesseract
    print("✅ PyTesseract imported")
except ImportError as e:
    print(f"❌ PyTesseract failed: {e}")

try:
    import fitz
    print("✅ PyMuPDF imported")
except ImportError as e:
    print(f"❌ PyMuPDF failed: {e}")

try:
    import cv2
    print("✅ OpenCV imported")
except ImportError as e:
    print(f"❌ OpenCV failed: {e}")

try:
    from pdf_processor import PDFProcessor
    print("✅ PDF Processor imported")
except ImportError as e:
    print(f"❌ PDF Processor failed: {e}")

try:
    from app import app
    print("✅ Flask app imported")
except ImportError as e:
    print(f"❌ Flask app failed: {e}")

print("\nAll imports tested!")
print("\nTo start the backend server, run:")
print("python app.py")
