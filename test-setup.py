#!/usr/bin/env python3
"""
Test script to verify the installation and setup
"""

import subprocess
import sys
import os
import json

def run_command(command, description, capture_output=True):
    """Run a command and return success status"""
    print(f"Testing {description}...", end=" ")
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(command, shell=True, check=True)
        print("✅ PASS")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAIL")
        if capture_output and e.stderr:
            print(f"   Error: {e.stderr.strip()}")
        return False
    except FileNotFoundError:
        print("❌ FAIL (Command not found)")
        return False

def test_python_imports():
    """Test Python package imports"""
    packages = [
        ('flask', 'Flask'),
        ('pandas', 'Pandas'),
        ('tabula', 'Tabula-py'),
        ('camelot', 'Camelot-py'),
        ('pytesseract', 'PyTesseract'),
        ('cv2', 'OpenCV'),
        ('fitz', 'PyMuPDF'),
        ('PIL', 'Pillow')
    ]
    
    print("\n📦 Testing Python Package Imports:")
    all_passed = True
    
    for package, name in packages:
        print(f"   {name}...", end=" ")
        try:
            __import__(package)
            print("✅ PASS")
        except ImportError as e:
            print("❌ FAIL")
            print(f"      Error: {e}")
            all_passed = False
    
    return all_passed

def test_system_dependencies():
    """Test system-level dependencies"""
    print("\n🔧 Testing System Dependencies:")
    
    tests = [
        ("python --version", "Python"),
        ("java -version", "Java"),
        ("tesseract --version", "Tesseract OCR"),
        ("node --version", "Node.js"),
        ("npm --version", "npm")
    ]
    
    results = []
    for command, name in tests:
        results.append(run_command(command, name))
    
    return all(results)

def test_project_structure():
    """Test project file structure"""
    print("\n📁 Testing Project Structure:")
    
    required_files = [
        "package.json",
        "src/App.tsx",
        "src/services/api.ts",
        "backend/app.py",
        "backend/pdf_processor.py",
        "backend/requirements.txt"
    ]
    
    all_exist = True
    for file_path in required_files:
        print(f"   {file_path}...", end=" ")
        if os.path.exists(file_path):
            print("✅ PASS")
        else:
            print("❌ FAIL")
            all_exist = False
    
    return all_exist

def test_api_health():
    """Test if the API server is running"""
    print("\n🌐 Testing API Health:")
    
    try:
        import requests
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'healthy':
                print("   API Health Check... ✅ PASS")
                return True
        print("   API Health Check... ❌ FAIL (Unhealthy response)")
        return False
    except ImportError:
        print("   API Health Check... ⚠️  SKIP (requests not installed)")
        return True  # Don't fail the test for this
    except Exception as e:
        print(f"   API Health Check... ❌ FAIL ({e})")
        return False

def main():
    """Main test function"""
    print("🧪 Bank to Tally Converter - Setup Test")
    print("=" * 50)
    
    tests = [
        ("System Dependencies", test_system_dependencies),
        ("Project Structure", test_project_structure),
        ("Python Imports", test_python_imports),
        ("API Health", test_api_health)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nTo start the application:")
        print("   Windows: start.bat")
        print("   macOS/Linux: ./start.sh")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("\nCommon solutions:")
        print("   - Install missing dependencies")
        print("   - Run 'cd backend && python setup.py'")
        print("   - Start the backend server: 'cd backend && python app.py'")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
